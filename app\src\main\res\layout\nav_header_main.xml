<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="176dp"
    android:background="@color/primary_color"
    android:gravity="bottom"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/circle_background_white"
        android:contentDescription="@string/app_name"
        android:padding="16dp"
        app:srcCompat="@drawable/ic_person"
        app:tint="@color/primary_color" />

    <TextView
        android:id="@+id/tvUserName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:text="نام کاربر"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvUserRole"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="شریک کسب‌وکار"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:alpha="0.8" />

</LinearLayout>
