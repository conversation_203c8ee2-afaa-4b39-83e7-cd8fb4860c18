@echo off
echo پاک کردن کامل Gradle Cache و فایل‌های قفل شده...

REM کشتن تمام پروسه‌های Java و Gradle
taskkill /f /im java.exe 2>nul
taskkill /f /im gradle.exe 2>nul
taskkill /f /im gradlew.exe 2>nul

echo منتظر بمانید...
timeout /t 3 /nobreak >nul

REM پاک کردن gradle cache
echo پاک کردن Gradle Cache...
rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
rmdir /s /q "%USERPROFILE%\.gradle\daemon" 2>nul
rmdir /s /q "%USERPROFILE%\.gradle\wrapper" 2>nul

REM پاک کردن build folders
echo پاک کردن Build Folders...
rmdir /s /q "build" 2>nul
rmdir /s /q "app\build" 2>nul
rmdir /s /q ".gradle" 2>nul

REM پاک کردن فایل‌های موقت
echo پاک کردن فایل‌های موقت...
del /q /s "*.tmp" 2>nul
del /q /s "*.lock" 2>nul

echo تمام Cache ها پاک شدند!
echo
echo مراحل بعدی:
echo 1. Android Studio را کاملاً ببندید
echo 2. کامپیوتر را restart کنید
echo 3. Android Studio را باز کنید
echo 4. File ^> Sync Project with Gradle Files
echo
pause
