<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_expenses"
            android:icon="@drawable/ic_expenses"
            android:title="@string/menu_expenses" />
        <item
            android:id="@+id/nav_inventory"
            android:icon="@drawable/ic_inventory"
            android:title="@string/menu_inventory" />
        <item
            android:id="@+id/nav_personal_withdrawal"
            android:icon="@drawable/ic_withdrawal"
            android:title="@string/menu_personal_withdrawal" />
        <item
            android:id="@+id/nav_notifications"
            android:icon="@drawable/ic_notifications"
            android:title="@string/menu_notifications" />
    </group>

    <item android:title="تنظیمات">
        <menu>
            <item
                android:id="@+id/nav_logout"
                android:icon="@drawable/ic_logout"
                android:title="@string/logout" />
        </menu>
    </item>

</menu>
