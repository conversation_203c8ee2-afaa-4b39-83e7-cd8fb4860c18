package com.example.ma.data.repository

import android.content.Context
import android.content.SharedPreferences
import com.example.ma.data.models.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository برای مدیریت احراز هویت
 */
class AuthRepository(context: Context) {

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)

    private val client = SupabaseClient.client

    companion object {
        private const val KEY_CURRENT_USER_ID = "current_user_id"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
    }
    
    /**
     * ورود کاربر با نام کاربری و رمز (فعلاً بدون Supabase)
     */
    suspend fun login(username: String, password: String): User? = withContext(Dispatchers.IO) {
        // بررسی اعتبار نام کاربری و رمز با داده‌های محلی
        val user = when {
            username == User.ALIKAKAI && password == User.ALIKAKAI -> {
                User.getAllUsers().find { it.username == User.ALIKAKAI }?.copy(isActive = true)
            }
            username == User.MILADNASIRI && password == User.MILADNASIRI -> {
                User.getAllUsers().find { it.username == User.MILADNASIRI }?.copy(isActive = true)
            }
            else -> null
        }

        // ذخیره اطلاعات ورود
        user?.let {
            sharedPreferences.edit()
                .putString(KEY_CURRENT_USER_ID, it.id)
                .putBoolean(KEY_IS_LOGGED_IN, true)
                .apply()
        }

        return@withContext user
    }
    
    /**
     * خروج کاربر
     */
    fun logout() {
        sharedPreferences.edit()
            .remove(KEY_CURRENT_USER_ID)
            .putBoolean(KEY_IS_LOGGED_IN, false)
            .apply()
    }
    
    /**
     * بررسی وضعیت ورود
     */
    fun isLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    }
    
    /**
     * دریافت کاربر فعلی (بدون suspend - برای UI)
     */
    fun getCurrentUserSync(): User? {
        val userId = sharedPreferences.getString(KEY_CURRENT_USER_ID, null)
        return userId?.let { id ->
            User.getAllUsers().find { it.id == id }?.copy(isActive = true)
        }
    }

    /**
     * دریافت کاربر فعلی (ساده)
     */
    suspend fun getCurrentUser(): User? = withContext(Dispatchers.IO) {
        val userId = sharedPreferences.getString(KEY_CURRENT_USER_ID, null)
        return@withContext userId?.let { id ->
            User.getAllUsers().find { it.id == id }?.copy(isActive = true)
        }
    }

    /**
     * دریافت کاربر مقابل
     */
    suspend fun getOtherUser(): User? = withContext(Dispatchers.IO) {
        val currentUserId = sharedPreferences.getString(KEY_CURRENT_USER_ID, null)
        return@withContext User.getAllUsers().find { it.id != currentUserId }
    }
}
}
