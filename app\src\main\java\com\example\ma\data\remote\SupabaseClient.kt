package com.example.ma.data.remote

import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import com.google.gson.Gson
import java.io.IOException

/**
 * کلاینت Supabase برای اتصال به دیتابیس
 * این کلاس اتصال به پایگاه داده Supabase را مدیریت می‌کند
 */
object SupabaseClient {

    // اطلاعات اتصال به Supabase - اطلاعات واقعی پروژه
    private const val SUPABASE_URL = "https://fikgmfrfdqdaddhngodc.supabase.co"
    private const val SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpa2dtZnJmZHFkYWRkaG5nb2RjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1NDY0MjgsImV4cCI6MjA2NzEyMjQyOH0._uNyI6lxP7w9fc8PW-IMJ24Mdr0xvdxnWxEMav8LuP0"

    private val client = OkHttpClient()
    private val gson = Gson()

    /**
     * ارسال درخواست GET به Supabase
     */
    fun get(table: String, callback: (String?) -> Unit) {
        val request = Request.Builder()
            .url("$SUPABASE_URL/rest/v1/$table")
            .addHeader("apikey", SUPABASE_ANON_KEY)
            .addHeader("Authorization", "Bearer $SUPABASE_ANON_KEY")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                callback(null)
            }

            override fun onResponse(call: Call, response: Response) {
                callback(response.body?.string())
            }
        })
    }

    /**
     * ارسال درخواست POST به Supabase
     */
    fun post(table: String, data: Any, callback: (Boolean) -> Unit) {
        val json = gson.toJson(data)
        val body = json.toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url("$SUPABASE_URL/rest/v1/$table")
            .addHeader("apikey", SUPABASE_ANON_KEY)
            .addHeader("Authorization", "Bearer $SUPABASE_ANON_KEY")
            .addHeader("Content-Type", "application/json")
            .addHeader("Prefer", "return=minimal")
            .post(body)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                callback(false)
            }

            override fun onResponse(call: Call, response: Response) {
                callback(response.isSuccessful)
            }
        })
    }
}
