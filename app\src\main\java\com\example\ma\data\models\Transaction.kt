package com.example.ma.data.models

import java.util.Date

/**
 * مدل تراکنش برای ثبت فروش، خرید، پرداختی و دریافتی
 * این کلاس تمام اطلاعات مربوط به تراکنش‌های مالی را نگهداری می‌کند
 */
data class Transaction(
    val id: String = "",                    // شناسه یکتای تراکنش
    val type: TransactionType,              // نوع تراکنش (فروش، خرید، پرداختی، دریافتی)
    val amount: Double,                     // مبلغ تراکنش به تومان
    val description: String,                // توضیحات تراکنش
    val userId: String,                     // شناسه کاربری که تراکنش را ثبت کرده
    val date: Date = Date(),                // تاریخ و زمان تراکنش
    val isApproved: Boolean = false,        // وضعیت تایید تراکنش
    val approvedBy: String? = null,         // شناسه کاربری که تراکنش را تایید کرده
    val approvedAt: Date? = null,           // تاریخ و زمان تایید
    val category: String? = null            // دسته‌بندی هزینه (اختیاری)
)

/**
 * انواع تراکنش‌های مالی در سیستم
 * @param displayName نام فارسی نوع تراکنش
 */
enum class TransactionType(val displayName: String) {
    SALE("فروش بطری"),           // فروش محصول
    PURCHASE("خرید مواد اولیه"),   // خرید مواد اولیه
    EXPENSE("پرداختی"),          // سایر پرداختی‌ها
    INCOME("دریافتی")            // سایر دریافتی‌ها
}

/**
 * مدل اعلان برای اطلاع‌رسانی تراکنش‌ها به شریک
 * هر تراکنش جدید باعث ایجاد اعلان برای طرف مقابل می‌شود
 */
data class Notification(
    val id: String = "",                    // شناسه یکتای اعلان
    val transactionId: String,              // شناسه تراکنش مربوطه
    val fromUserId: String,                 // شناسه کاربر فرستنده
    val toUserId: String,                   // شناسه کاربر گیرنده
    val message: String,                    // متن پیام اعلان
    val isRead: Boolean = false,            // وضعیت خوانده شدن
    val createdAt: Date = Date()            // تاریخ و زمان ایجاد اعلان
)
