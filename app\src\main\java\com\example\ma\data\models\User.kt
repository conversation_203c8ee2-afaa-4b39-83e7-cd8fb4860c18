package com.example.ma.data.models

/**
 * مدل کاربر برای دو شریک کسب‌وکار
 * این کلاس اطلاعات کاربران سیستم را نگهداری می‌کند
 */
data class User(
    val id: String,                    // شناسه یکتای کاربر
    val username: String,              // نام کاربری
    val displayName: String,           // نام نمایشی فارسی
    val isActive: Boolean = false      // وضعیت فعال بودن کاربر
) {
    companion object {
        // نام‌های کاربری ثابت
        const val ALIKAKAI = "Alikakai"
        const val MILADNASIRI = "Miladnasiri"

        /**
         * دریافت لیست همه کاربران سیستم با ID های واقعی از Supabase
         * @return لیست کاربران
         */
        fun getAllUsers() = listOf(
            User(id = "eaf14ca9-17bc-4f7f-a572-22f2a4552f8c", username = ALIKAKAI, displayName = "علی کاکایی"),
            User(id = "15d4f25d-8c5b-4ebb-8abd-6853007fcc36", username = MILADNASIRI, displayName = "میلاد نصیری")
        )
    }
}
