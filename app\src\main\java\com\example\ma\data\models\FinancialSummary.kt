package com.example.ma.data.models

/**
 * خلاصه مالی برای محاسبه سهم هر شریک
 * این کلاس تمام اطلاعات مالی کسب‌وکار را خلاصه می‌کند
 * و سهم عادلانه هر شریک را محاسبه می‌کند
 */
data class FinancialSummary(
    val totalSales: Double = 0.0,                          // کل فروش بطری‌ها
    val totalPurchases: Double = 0.0,                      // کل خرید مواد اولیه
    val totalExpenses: Double = 0.0,                       // کل پرداختی‌ها
    val totalIncome: Double = 0.0,                         // کل دریافتی‌ها
    val netProfit: Double = 0.0,                           // سود خالص
    val userExpenses: Map<String, Double> = emptyMap(),    // هزینه‌های شخصی هر کاربر
    val userShare: Map<String, Double> = emptyMap()        // سهم نهایی هر کاربر
) {
    /**
     * محاسبه سهم عادلانه 50-50 با در نظر گیری هزینه‌های نابرابر
     * اگر یک شریک بیشتر هزینه کرده، از سهمش کم می‌شود
     *
     * @param user1Id شناسه کاربر اول
     * @param user2Id شناسه کاربر دوم
     * @return نقشه‌ای از شناسه کاربر به سهم نهایی او
     */
    fun calculateShares(user1Id: String, user2Id: String): Map<String, Double> {
        val user1Expenses = userExpenses[user1Id] ?: 0.0    // هزینه کاربر اول
        val user2Expenses = userExpenses[user2Id] ?: 0.0    // هزینه کاربر دوم

        // محاسبه سود خالص: (فروش + دریافتی) - (خرید + پرداختی)
        val profit = totalSales + totalIncome - totalPurchases - totalExpenses

        // تقسیم سود به نصف (سهم پایه هر شریک)
        val halfProfit = profit / 2

        // محاسبه سهم نهایی با تعدیل هزینه‌های نابرابر
        // اگر کاربر اول بیشتر هزینه کرده، از سهم او کم می‌شود
        val user1FinalShare = halfProfit - (user1Expenses - user2Expenses) / 2
        val user2FinalShare = halfProfit - (user2Expenses - user1Expenses) / 2

        return mapOf(
            user1Id to user1FinalShare,
            user2Id to user2FinalShare
        )
    }
}
