<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/background_color">

    <!-- Logo or Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_title"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/primary_color"
        android:layout_marginBottom="48dp"
        android:fontFamily="sans-serif-medium" />

    <!-- Login Form Card -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Username Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/username_hint"
                app:startIconDrawable="@drawable/ic_person"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etUsername"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Password Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="@string/password_hint"
                app:startIconDrawable="@drawable/ic_lock"
                app:endIconMode="password_toggle"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Login Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLogin"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/login_button"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="12dp"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Error Message -->
    <TextView
        android:id="@+id/tvError"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textColor="@color/error_color"
        android:textSize="14sp"
        android:visibility="gone" />

</LinearLayout>
