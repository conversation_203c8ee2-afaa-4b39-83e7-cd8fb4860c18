package com.example.ma.ui.auth

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.example.ma.MainActivity
import com.example.ma.R
import com.example.ma.databinding.ActivityLoginBinding

/**
 * صفحه ورود به سیستم
 * این صفحه احراز هویت کاربران را انجام می‌دهد
 */
class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding
    private lateinit var viewModel: LoginViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // راه‌اندازی ViewModel
        viewModel = ViewModelProvider(this)[LoginViewModel::class.java]

        setupUI()
        observeViewModel()

        // بررسی اینکه آیا کاربر قبلاً وارد شده یا نه
        if (viewModel.isUserLoggedIn()) {
            navigateToMain()
        }
    }
    
    private fun setupUI() {
        // تنظیم دکمه ورود
        binding.btnLogin.setOnClickListener {
            val username = binding.etUsername.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()

            if (validateInput(username, password)) {
                hideError()
                viewModel.login(username, password)
            }
        }

        // پاک کردن خطاها هنگام تایپ
        binding.etUsername.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) hideError()
        }

        binding.etPassword.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) hideError()
        }
    }
    
    private fun observeViewModel() {
        viewModel.loginResult.observe(this) { result ->
            when (result) {
                is LoginResult.Success -> {
                    hideError()
                    showSuccess("خوش آمدید ${result.user.displayName}")
                    navigateToMain()
                }
                is LoginResult.Error -> {
                    showError(result.message)
                    enableLoginButton()
                }
                is LoginResult.Loading -> {
                    disableLoginButton()
                }
            }
        }
    }
    
    private fun validateInput(username: String, password: String): Boolean {
        // پاک کردن خطاهای قبلی
        binding.etUsername.error = null
        binding.etPassword.error = null

        return when {
            username.isEmpty() -> {
                binding.etUsername.error = "نام کاربری را وارد کنید"
                binding.etUsername.requestFocus()
                false
            }
            password.isEmpty() -> {
                binding.etPassword.error = "رمز عبور را وارد کنید"
                binding.etPassword.requestFocus()
                false
            }
            username.length < 3 -> {
                binding.etUsername.error = "نام کاربری باید حداقل 3 کاراکتر باشد"
                binding.etUsername.requestFocus()
                false
            }
            password.length < 3 -> {
                binding.etPassword.error = "رمز عبور باید حداقل 3 کاراکتر باشد"
                binding.etPassword.requestFocus()
                false
            }
            else -> true
        }
    }

    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    private fun hideError() {
        binding.tvError.visibility = View.GONE
    }

    private fun showSuccess(message: String) {
        // می‌توان از Toast یا Snackbar استفاده کرد
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun disableLoginButton() {
        binding.btnLogin.isEnabled = false
        binding.btnLogin.text = "در حال ورود..."
    }

    private fun enableLoginButton() {
        binding.btnLogin.isEnabled = true
        binding.btnLogin.text = getString(R.string.login_button)
    }

    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}
