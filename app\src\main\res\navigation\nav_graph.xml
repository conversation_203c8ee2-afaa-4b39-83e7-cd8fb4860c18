<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/nav_dashboard">

    <fragment
        android:id="@+id/nav_dashboard"
        android:name="com.example.ma.ui.dashboard.DashboardFragment"
        android:label="@string/dashboard"
        tools:layout="@layout/fragment_dashboard" />

    <fragment
        android:id="@+id/nav_transactions"
        android:name="com.example.ma.ui.transactions.TransactionsFragment"
        android:label="@string/transactions"
        tools:layout="@layout/fragment_transactions" />

    <fragment
        android:id="@+id/nav_notifications"
        android:name="com.example.ma.ui.notifications.NotificationsFragment"
        android:label="@string/notifications"
        tools:layout="@layout/fragment_notifications" />

    <fragment
        android:id="@+id/nav_reports"
        android:name="com.example.ma.ui.reports.ReportsFragment"
        android:label="@string/reports"
        tools:layout="@layout/fragment_reports" />

</navigation>
