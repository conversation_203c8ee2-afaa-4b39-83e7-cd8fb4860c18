<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- محتوای اصلی -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- AppBar -->
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/primary_color"
                app:title="@string/app_name"
                app:titleTextColor="@android:color/white"
                app:navigationIcon="@drawable/ic_menu" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- محتوای اصلی صفحه -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- هدر کاربر -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_person"
                        android:background="@drawable/circle_background"
                        android:padding="8dp"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/tvCurrentUser"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="در حال بارگذاری..."
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <!-- آمارهای کلی -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    <!-- موجودی انبار -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:cardBackgroundColor="@color/surface_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp"
                            android:gravity="center">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_inventory"
                                android:layout_marginBottom="8dp"
                                app:tint="@color/primary_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/inventory_stock"
                                android:textSize="12sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/tvInventoryCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:textColor="@color/primary_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/bottles_unit"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- موجودی کل -->
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp"
                        app:cardBackgroundColor="@color/surface_color">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp"
                            android:gravity="center">

                            <ImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_money"
                                android:layout_marginBottom="8dp"
                                app:tint="@color/success_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_balance"
                                android:textSize="12sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/tvTotalBalance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/success_color" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/currency_unit"
                                android:textSize="10sp"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- بدهکاری/طلبکاری شخصی -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/surface_color">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_balance"
                            android:layout_marginEnd="12dp"
                            app:tint="@color/warning_color" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/account_balance"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/tvPersonalBalance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0 تومان"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- فرم فروش -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/surface_color">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- عنوان فروش -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sale"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary_color"
                            android:layout_marginBottom="16dp" />

                        <!-- تعداد بطری -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="12dp"
                            android:hint="@string/bottle_count_hint"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etBottleCount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number"
                                android:maxLines="1" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- قیمت -->
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="12dp"
                            android:hint="@string/price_hint"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etPrice"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number"
                                android:maxLines="1" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <!-- نوع تراکنش -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/transaction_type"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp" />

                        <RadioGroup
                            android:id="@+id/rgPaymentType"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <RadioButton
                                android:id="@+id/rbCash"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/cash_payment"
                                android:textSize="14sp" />

                            <RadioButton
                                android:id="@+id/rbCard"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/card_payment"
                                android:textSize="14sp" />

                        </RadioGroup>

                        <!-- انتخاب دریافت کننده/کارت -->
                        <TextView
                            android:id="@+id/tvReceiverLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/cash_receiver"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="8dp"
                            android:visibility="gone" />

                        <Spinner
                            android:id="@+id/spinnerReceiver"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:layout_marginBottom="16dp"
                            android:visibility="gone" />

                        <!-- دکمه ثبت -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnRegisterTransaction"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:text="@string/register_transaction"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:cornerRadius="12dp"
                            style="@style/Widget.MaterialComponents.Button" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/activity_main_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>