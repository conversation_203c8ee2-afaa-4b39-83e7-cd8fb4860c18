-- حذف جداول موجود (اگر وجود دارند)
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS users;

-- جدول کاربران
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول تراکنش‌ها
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    type VARCHAR(20) NOT NULL CHECK (type IN ('SALE', 'PURCHASE', 'EXPENSE', 'INCOME')),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    description TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_approved BOOLEAN DEFAULT false,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    category VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول اعلانات
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    from_user_id UUID NOT NULL REFERENCES users(id),
    to_user_id UUID NOT NULL REFERENCES users(id),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- درج کاربران اولیه
INSERT INTO users (username, display_name, password_hash) VALUES 
('Alikakai', 'علی کاکایی', 'Alikakai'),
('Miladnasiri', 'میلاد نصیری', 'Miladnasiri');

-- ایجاد ایندکس‌ها برای بهبود عملکرد
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_date ON transactions(date);
CREATE INDEX idx_notifications_to_user ON notifications(to_user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

-- تابع برای آپدیت updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تریگرها برای آپدیت خودکار updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تابع برای ایجاد اعلان خودکار هنگام ثبت تراکنش جدید
CREATE OR REPLACE FUNCTION create_transaction_notification()
RETURNS TRIGGER AS $$
DECLARE
    other_user_id UUID;
    notification_message TEXT;
BEGIN
    -- پیدا کردن کاربر مقابل
    SELECT id INTO other_user_id 
    FROM users 
    WHERE id != NEW.user_id 
    LIMIT 1;
    
    -- ساخت پیام اعلان
    notification_message := 'تراکنش جدید: ' || 
        CASE NEW.type
            WHEN 'SALE' THEN 'فروش بطری'
            WHEN 'PURCHASE' THEN 'خرید مواد اولیه'
            WHEN 'EXPENSE' THEN 'پرداختی'
            WHEN 'INCOME' THEN 'دریافتی'
        END || 
        ' به مبلغ ' || NEW.amount || ' تومان - ' || NEW.description;
    
    -- درج اعلان
    INSERT INTO notifications (transaction_id, from_user_id, to_user_id, message)
    VALUES (NEW.id, NEW.user_id, other_user_id, notification_message);
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تریگر برای ایجاد اعلان خودکار
CREATE TRIGGER create_notification_on_transaction 
    AFTER INSERT ON transactions
    FOR EACH ROW EXECUTE FUNCTION create_transaction_notification();

-- فعال‌سازی Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- سیاست‌های دسترسی (همه کاربران می‌توانند همه چیز را ببینند)
CREATE POLICY "Enable all access for users" ON users FOR ALL USING (true);
CREATE POLICY "Enable all access for transactions" ON transactions FOR ALL USING (true);
CREATE POLICY "Enable all access for notifications" ON notifications FOR ALL USING (true);
