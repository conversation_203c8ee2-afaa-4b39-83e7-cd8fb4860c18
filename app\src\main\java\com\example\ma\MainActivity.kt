package com.example.ma

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GravityCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.example.ma.data.models.TransactionType
import com.example.ma.data.models.User
import com.example.ma.data.repository.AuthRepository
import com.example.ma.ui.auth.LoginActivity
import com.example.ma.ui.main.MainViewModel
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.button.MaterialButton
import com.google.android.material.navigation.NavigationView
import com.google.android.material.textfield.TextInputEditText
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var authRepository: AuthRepository
    private lateinit var viewModel: MainViewModel
    private lateinit var drawerLayout: DrawerLayout
    private lateinit var currentUser: User

    // UI Elements
    private lateinit var tvCurrentUser: TextView
    private lateinit var tvInventoryCount: TextView
    private lateinit var tvTotalBalance: TextView
    private lateinit var tvPersonalBalance: TextView
    private lateinit var etBottleCount: TextInputEditText
    private lateinit var etPrice: TextInputEditText
    private lateinit var rgPaymentType: RadioGroup
    private lateinit var rbCash: RadioButton
    private lateinit var rbCard: RadioButton
    private lateinit var tvReceiverLabel: TextView
    private lateinit var spinnerReceiver: Spinner
    private lateinit var btnRegisterTransaction: MaterialButton

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        authRepository = AuthRepository(this)

        // بررسی وضعیت ورود کاربر
        if (!authRepository.isLoggedIn()) {
            navigateToLogin()
            return
        }

        // دریافت کاربر فعلی
        currentUser = authRepository.getCurrentUserSync() ?: run {
            navigateToLogin()
            return
        }

        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.drawer_layout)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // راه‌اندازی ViewModel
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]

        setupUI()
        setupDrawer()
        observeViewModel()
        loadData()
    }

    private fun setupUI() {
        // پیدا کردن View ها
        tvCurrentUser = findViewById(R.id.tvCurrentUser)
        tvInventoryCount = findViewById(R.id.tvInventoryCount)
        tvTotalBalance = findViewById(R.id.tvTotalBalance)
        tvPersonalBalance = findViewById(R.id.tvPersonalBalance)
        etBottleCount = findViewById(R.id.etBottleCount)
        etPrice = findViewById(R.id.etPrice)
        rgPaymentType = findViewById(R.id.rgPaymentType)
        rbCash = findViewById(R.id.rbCash)
        rbCard = findViewById(R.id.rbCard)
        tvReceiverLabel = findViewById(R.id.tvReceiverLabel)
        spinnerReceiver = findViewById(R.id.spinnerReceiver)
        btnRegisterTransaction = findViewById(R.id.btnRegisterTransaction)

        // نمایش کاربر فعلی
        tvCurrentUser.text = currentUser.displayName

        // تنظیم RadioGroup
        rgPaymentType.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rbCash -> {
                    tvReceiverLabel.text = getString(R.string.cash_receiver)
                    tvReceiverLabel.visibility = View.VISIBLE
                    spinnerReceiver.visibility = View.VISIBLE
                    setupCashSpinner()
                }
                R.id.rbCard -> {
                    tvReceiverLabel.text = getString(R.string.card_destination)
                    tvReceiverLabel.visibility = View.VISIBLE
                    spinnerReceiver.visibility = View.VISIBLE
                    setupCardSpinner()
                }
            }
        }

        // دکمه ثبت تراکنش
        btnRegisterTransaction.setOnClickListener {
            registerTransaction()
        }
    }

    private fun setupDrawer() {
        drawerLayout = findViewById(R.id.drawer_layout)
        val toolbar = findViewById<MaterialToolbar>(R.id.toolbar)
        val navView = findViewById<NavigationView>(R.id.nav_view)

        setSupportActionBar(toolbar)

        val toggle = ActionBarDrawerToggle(
            this, drawerLayout, toolbar,
            R.string.app_name, R.string.app_name
        )
        drawerLayout.addDrawerListener(toggle)
        toggle.syncState()

        navView.setNavigationItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.nav_expenses -> {
                    // TODO: باز کردن صفحه هزینه‌ها
                    Toast.makeText(this, "هزینه‌ها", Toast.LENGTH_SHORT).show()
                }
                R.id.nav_inventory -> {
                    // TODO: باز کردن صفحه انبار
                    Toast.makeText(this, "انبار گردانی", Toast.LENGTH_SHORT).show()
                }
                R.id.nav_personal_withdrawal -> {
                    // TODO: باز کردن صفحه برداشت شخصی
                    Toast.makeText(this, "برداشت شخصی", Toast.LENGTH_SHORT).show()
                }
                R.id.nav_notifications -> {
                    // TODO: باز کردن صفحه اعلانات
                    Toast.makeText(this, "اعلانات", Toast.LENGTH_SHORT).show()
                }
                R.id.nav_logout -> {
                    logout()
                }
            }
            drawerLayout.closeDrawer(GravityCompat.START)
            true
        }
    }

    private fun setupCashSpinner() {
        val users = User.getAllUsers()
        val userNames = users.map { it.displayName }
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, userNames)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerReceiver.adapter = adapter
    }

    private fun setupCardSpinner() {
        val cardOptions = listOf(
            getString(R.string.ali_kakai_card),
            getString(R.string.milad_nasiri_card)
        )
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, cardOptions)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerReceiver.adapter = adapter
    }

    private fun observeViewModel() {
        // TODO: مشاهده تغییرات ViewModel
    }

    private fun loadData() {
        // TODO: بارگذاری داده‌ها از سرور
        // فعلاً مقادیر تست
        tvInventoryCount.text = "150"
        tvTotalBalance.text = "2,500,000"

        // محاسبه بدهکاری/طلبکاری (مثال)
        val personalBalance = 0 // TODO: محاسبه واقعی
        tvPersonalBalance.text = formatBalance(personalBalance)
        tvPersonalBalance.setTextColor(
            if (personalBalance >= 0)
                getColor(R.color.success_color)
            else
                getColor(R.color.error_color)
        )
    }

    private fun registerTransaction() {
        val bottleCountStr = etBottleCount.text.toString().trim()
        val priceStr = etPrice.text.toString().trim()

        // اعتبارسنجی
        if (!validateInput(bottleCountStr, priceStr)) {
            return
        }

        val bottleCount = bottleCountStr.toInt()
        val price = priceStr.toDouble()
        val totalAmount = bottleCount * price

        val paymentType = when (rgPaymentType.checkedRadioButtonId) {
            R.id.rbCash -> "نقدی"
            R.id.rbCard -> "کارت به کارت"
            else -> {
                Toast.makeText(this, getString(R.string.please_select_payment_type), Toast.LENGTH_SHORT).show()
                return
            }
        }

        val receiver = spinnerReceiver.selectedItem?.toString()
        if (receiver.isNullOrEmpty()) {
            Toast.makeText(this, getString(R.string.please_select_receiver), Toast.LENGTH_SHORT).show()
            return
        }

        // ثبت تراکنش
        lifecycleScope.launch {
            val description = "فروش $bottleCount بطری - $paymentType - $receiver"
            val success = viewModel.registerSaleTransaction(
                amount = totalAmount,
                description = description,
                userId = currentUser.id,
                bottleCount = bottleCount,
                paymentType = paymentType,
                receiver = receiver
            )

            if (success) {
                Toast.makeText(this@MainActivity, getString(R.string.transaction_sent_for_approval), Toast.LENGTH_LONG).show()
                clearForm()
                loadData() // بروزرسانی آمارها
            } else {
                Toast.makeText(this@MainActivity, getString(R.string.connection_error), Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun validateInput(bottleCountStr: String, priceStr: String): Boolean {
        if (bottleCountStr.isEmpty()) {
            etBottleCount.error = getString(R.string.please_enter_bottle_count)
            etBottleCount.requestFocus()
            return false
        }

        if (priceStr.isEmpty()) {
            etPrice.error = getString(R.string.please_enter_price)
            etPrice.requestFocus()
            return false
        }

        try {
            val bottleCount = bottleCountStr.toInt()
            val price = priceStr.toDouble()

            if (bottleCount <= 0) {
                etBottleCount.error = "تعداد باید بیشتر از صفر باشد"
                etBottleCount.requestFocus()
                return false
            }

            if (price <= 0) {
                etPrice.error = "قیمت باید بیشتر از صفر باشد"
                etPrice.requestFocus()
                return false
            }

        } catch (e: NumberFormatException) {
            Toast.makeText(this, getString(R.string.invalid_number), Toast.LENGTH_SHORT).show()
            return false
        }

        return true
    }

    private fun clearForm() {
        etBottleCount.text?.clear()
        etPrice.text?.clear()
        rgPaymentType.clearCheck()
        tvReceiverLabel.visibility = View.GONE
        spinnerReceiver.visibility = View.GONE
    }

    private fun formatBalance(amount: Int): String {
        return if (amount >= 0) {
            "+${String.format("%,d", amount)} تومان"
        } else {
            "${String.format("%,d", amount)} تومان"
        }
    }

    private fun logout() {
        authRepository.logout()
        navigateToLogin()
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START)
        } else {
            super.onBackPressed()
        }
    }
}

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}