package com.example.ma.data.repository

import com.example.ma.data.models.Notification
import com.example.ma.data.remote.SupabaseClient
import io.github.jan.supabase.postgrest.from
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import java.util.Date

/**
 * Repository برای مدیریت اعلانات
 */
class NotificationRepository {
    
    private val client = SupabaseClient.client
    
    /**
     * دریافت اعلانات یک کاربر
     */
    suspend fun getNotificationsForUser(userId: String): List<Notification> = withContext(Dispatchers.IO) {
        try {
            val response = client.from("notifications")
                .select()
                .eq("to_user_id", userId)
                .order("created_at", ascending = false)
                .decodeList<NotificationDto>()
            
            response.map { it.toNotification() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * علامت‌گذاری اعلان به عنوان خوانده شده
     */
    suspend fun markAsRead(notificationId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            client.from("notifications")
                .update(mapOf("is_read" to true))
                .eq("id", notificationId)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * شمارش اعلانات خوانده نشده
     */
    suspend fun getUnreadCount(userId: String): Int = withContext(Dispatchers.IO) {
        try {
            val response = client.from("notifications")
                .select()
                .eq("to_user_id", userId)
                .eq("is_read", false)
                .decodeList<NotificationDto>()
            
            response.size
        } catch (e: Exception) {
            0
        }
    }
}

/**
 * DTO برای ارتباط با Supabase
 */
@Serializable
data class NotificationDto(
    val id: String = "",
    val transaction_id: String,
    val from_user_id: String,
    val to_user_id: String,
    val message: String,
    val is_read: Boolean = false,
    val created_at: String = ""
) {
    fun toNotification(): Notification {
        return Notification(
            id = id,
            transactionId = transaction_id,
            fromUserId = from_user_id,
            toUserId = to_user_id,
            message = message,
            isRead = is_read,
            createdAt = Date() // TODO: Parse date properly
        )
    }
}
