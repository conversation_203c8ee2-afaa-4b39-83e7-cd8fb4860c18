package com.example.ma.data.repository

import com.example.ma.data.models.Transaction
import com.example.ma.data.models.TransactionType
import com.example.ma.data.remote.SupabaseClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import java.util.Date

/**
 * مخزن داده برای مدیریت تراکنش‌های مالی
 * این کلاس تمام عملیات مربوط به تراکنش‌ها را با دیتابیس انجام می‌دهد
 */
class TransactionRepository {

    /**
     * دریافت تمام تراکنش‌های ثبت شده در سیستم
     * @return لیست تراکنش‌ها
     */
    suspend fun getAllTransactions(): List<Transaction> = withContext(Dispatchers.IO) {
        suspendCancellableCoroutine { continuation ->
            SupabaseClient.get("transactions") { response ->
                if (response != null) {
                    try {
                        // TODO: Parse JSON response to Transaction list
                        continuation.resume(emptyList())
                    } catch (e: Exception) {
                        continuation.resume(emptyList())
                    }
                } else {
                    continuation.resume(emptyList())
                }
            }
        }
    }
    
    /**
     * دریافت تراکنش‌های مربوط به یک کاربر خاص
     * @param userId شناسه کاربر
     * @return لیست تراکنش‌های کاربر
     */
    suspend fun getTransactionsByUser(userId: String): List<Transaction> = withContext(Dispatchers.IO) {
        try {
            val response = client.from("transactions")
                .select()
                .eq("user_id", userId)
                .order("created_at", ascending = false)
                .decodeList<TransactionDto>()

            response.map { it.toTransaction() }
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * ثبت تراکنش جدید در سیستم
     * @param transaction اطلاعات تراکنش
     * @return true در صورت موفقیت، false در صورت خطا
     */
    suspend fun insertTransaction(transaction: Transaction): Boolean = withContext(Dispatchers.IO) {
        suspendCancellableCoroutine { continuation ->
            val transactionData = mapOf(
                "type" to transaction.type.name,
                "amount" to transaction.amount,
                "description" to transaction.description,
                "user_id" to transaction.userId
            )

            SupabaseClient.post("transactions", transactionData) { success ->
                continuation.resume(success)
            }
        }
    }

    /**
     * تایید تراکنش توسط شریک
     * @param transactionId شناسه تراکنش
     * @param approvedBy شناسه کاربر تایید کننده
     * @return true در صورت موفقیت
     */
    suspend fun approveTransaction(transactionId: String, approvedBy: String): Boolean = withContext(Dispatchers.IO) {
        // TODO: پیاده‌سازی بعداً
        return@withContext true
    }

    /**
     * رد تراکنش (حذف از سیستم)
     * @param transactionId شناسه تراکنش
     * @return true در صورت موفقیت
     */
    suspend fun rejectTransaction(transactionId: String): Boolean = withContext(Dispatchers.IO) {
        // TODO: پیاده‌سازی بعداً
        return@withContext true
    }
}
