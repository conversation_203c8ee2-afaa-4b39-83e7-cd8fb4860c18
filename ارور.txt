The client will now receive all logging from the daemon (pid: 13412). The daemon log file: C:\Users\<USER>\.gradle\daemon\7.6.1\daemon-13412.out.log
Starting 3rd build in daemon [uptime: 4 mins 6.728 secs, performance: 100%]
Using 2 worker leases.
Now considering [C:\Users\<USER>\AndroidStudioProjects\MA] as hierarchies to watch
Watching the file system is configured to be enabled if available
File system watching is active
Invalidating in-memory cache of C:\Users\<USER>\AndroidStudioProjects\MA\.gradle\7.6.1\fileHashes\fileHashes.bin
Invalidating in-memory cache of C:\Users\<USER>\AndroidStudioProjects\MA\.gradle\7.6.1\fileHashes\resourceHashesCache.bin
Invalidating in-memory cache of C:\Users\<USER>\.gradle\caches\journal-1\file-access.bin
Invalidating in-memory cache of C:\Users\<USER>\.gradle\caches\7.6.1\fileHashes\fileHashes.bin
Invalidating in-memory cache of C:\Users\<USER>\.gradle\caches\7.6.1\fileHashes\resourceHashesCache.bin
Starting Build
Invalidating in-memory cache of C:\Users\<USER>\.gradle\caches\7.6.1\kotlin-dsl\executionHistory.bin
Kotlin DSL script compilation (Settings/TopLevel/stage1) is not up-to-date because:
  No history is available.
Kotlin DSL script compilation (Settings/TopLevel/stage2) is not up-to-date because:
  No history is available.
Settings evaluated using settings file 'C:\Users\<USER>\AndroidStudioProjects\MA\settings.gradle.kts'.
Invalidating in-memory cache of C:\Users\<USER>\AndroidStudioProjects\MA\.gradle\7.6.1\dependencies-accessors\executionHistory.bin
Skipping generation of dependency accessors for libs as it is up-to-date.
Projects loaded. Root project using build file 'C:\Users\<USER>\AndroidStudioProjects\MA\build.gradle.kts'.
Included projects: [root project 'M&A', project ':app']

> Configure project :
Evaluating root project 'M&A' using build file 'C:\Users\<USER>\AndroidStudioProjects\MA\build.gradle.kts'.
Invalidating in-memory cache of C:\Users\<USER>\AndroidStudioProjects\MA\.gradle\buildOutputCleanup\outputFiles.bin
Caching disabled for Kotlin DSL plugin accessors for classpath 'ca879c54cc96919afb284619bf8b083a' because:
  Build cache is disabled
Skipping Kotlin DSL plugin accessors for classpath 'ca879c54cc96919afb284619bf8b083a' as it is up-to-date.
Caching disabled for Kotlin DSL script compilation (Project/TopLevel/stage1) because:
  Build cache is disabled
Skipping Kotlin DSL script compilation (Project/TopLevel/stage1) as it is up-to-date.
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/application/com.android.application.gradle.plugin/7.3.1/com.android.application.gradle.plugin-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/application/com.android.application.gradle.plugin/7.3.1/com.android.application.gradle.plugin-7.3.1.pom, took 1 s 207 ms
Downloading https://maven.aliyun.com/repository/google/com/android/application/com.android.application.gradle.plugin/7.3.1/com.android.application.gradle.plugin-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4202000786813854240bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/android/org.jetbrains.kotlin.android.gradle.plugin/1.8.20/org.jetbrains.kotlin.android.gradle.plugin-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10697089953944962106bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.pom, took 1 s 91 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8676624386878101352bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.module, took 334 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.module to C:\Users\<USER>\.gradle\.tmp\gradle_download1152102263701042061bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20.pom (4.85 kB / 23.17 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9881019935419132596bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20.pom, took 2 s 88 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugins-bom/1.8.20/kotlin-gradle-plugins-bom-1.8.20.pom, took 1 s 851 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugins-bom/1.8.20/kotlin-gradle-plugins-bom-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14054222008460370489bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugins-bom/1.8.20/kotlin-gradle-plugins-bom-1.8.20.module, took 1 s 864 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugins-bom/1.8.20/kotlin-gradle-plugins-bom-1.8.20.module to C:\Users\<USER>\.gradle\.tmp\gradle_download17484654295093426757bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20.module to C:\Users\<USER>\.gradle\.tmp\gradle_download8174411403052774630bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20.module, took 2 s 420 ms
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/sdk-common/30.3.1/sdk-common-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/sdk-common/30.3.1/sdk-common-30.3.1.pom, took 342 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/sdk-common/30.3.1/sdk-common-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12656566426065614975bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/sdklib/30.3.1/sdklib-30.3.1.pom]
Download https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/transform-api/2.0.0-deprecated-use-gradle-api/transform-api-2.0.0-deprecated-use-gradle-api.pom, took 294 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/transform-api/2.0.0-deprecated-use-gradle-api/transform-api-2.0.0-deprecated-use-gradle-api.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4159608621204037445bin
Download https://maven.aliyun.com/repository/google/com/android/tools/sdklib/30.3.1/sdklib-30.3.1.pom, took 343 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/sdklib/30.3.1/sdklib-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4315842790465976478bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpmime/4.5.6/httpmime-4.5.6.pom, took 300 ms
Download https://maven.aliyun.com/repository/gradle-plugin/commons-io/commons-io/2.4/commons-io-2.4.pom, took 302 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-io/commons-io/2.4/commons-io-2.4.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8317383394359394870bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpmime/4.5.6/httpmime-4.5.6.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5636690579278780363bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-client/4.5.6/httpcomponents-client-4.5.6.pom, took 414 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/25/commons-parent-25.pom (1.93 kB / 48.30 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-client/4.5.6/httpcomponents-client-4.5.6.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download18405792383155212534bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/25/commons-parent-25.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1943686611341206169bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/25/commons-parent-25.pom, took 803 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-parent/10/httpcomponents-parent-10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15039496328311276088bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-parent/10/httpcomponents-parent-10.pom, took 557 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/9/apache-9.pom, took 287 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/18/apache-18.pom, took 320 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/9/apache-9.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7945518992581594993bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/18/apache-18.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3091411459420431537bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm/9.1/asm-9.1.pom, took 321 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm/9.1/asm-9.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7252551865707239664bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-analysis/9.1/asm-analysis-9.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12876177382143935909bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-analysis/9.1/asm-analysis-9.1.pom, took 690 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/ow2/1.5/ow2-1.5.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7377574387134076180bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/ow2/1.5/ow2-1.5.pom, took 429 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-commons/9.1/asm-commons-9.1.pom, took 378 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-commons/9.1/asm-commons-9.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17118351229775081870bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/repository/30.3.1/repository-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/repository/30.3.1/repository-30.3.1.pom, took 341 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-util/9.1/asm-util-9.1.pom (2.94 kB / 2.94 kB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/repository/30.3.1/repository-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14738405036003105648bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-util/9.1/asm-util-9.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15536183169978077399bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-util/9.1/asm-util-9.1.pom, took 380 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-runtime/2.3.2/jaxb-runtime-2.3.2.pom, took 363 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-runtime/2.3.2/jaxb-runtime-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3828886011285534676bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/mvn/jaxb-runtime-parent/2.3.2/jaxb-runtime-parent-2.3.2.pom, took 345 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/mvn/jaxb-runtime-parent/2.3.2/jaxb-runtime-parent-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3942381988170590310bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcpkix-jdk15on/1.67/bcpkix-jdk15on-1.67.pom, took 1 s 136 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcpkix-jdk15on/1.67/bcpkix-jdk15on-1.67.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3526097488404022686bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/sf/jopt-simple/jopt-simple/4.9/jopt-simple-4.9.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12944586683565940691bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/mvn/jaxb-parent/2.3.2/jaxb-parent-2.3.2.pom, took 610 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/mvn/jaxb-parent/2.3.2/jaxb-parent-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5281659226090022479bin
Download https://maven.aliyun.com/repository/gradle-plugin/net/sf/jopt-simple/jopt-simple/4.9/jopt-simple-4.9.pom, took 542 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/jaxb-bom-ext/2.3.2/jaxb-bom-ext-2.3.2.pom, took 352 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/jaxb-bom-ext/2.3.2/jaxb-bom-ext-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16111756331019410083bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/sonatype/oss/oss-parent/7/oss-parent-7.pom, took 287 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/sonatype/oss/oss-parent/7/oss-parent-7.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17269215316908914712bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-bom/2.3.2/jaxb-bom-2.3.2.pom (7.33 kB / 8.02 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-bom/2.3.2/jaxb-bom-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15109545724093039397bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-bom/2.3.2/jaxb-bom-2.3.2.pom, took 393 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/eclipse/ee4j/project/1.0.5/project-1.0.5.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10896382138451241322bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/eclipse/ee4j/project/1.0.5/project-1.0.5.pom, took 519 ms
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/bundletool/1.9.0/bundletool-1.9.0.pom]
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/bundletool/1.9.0/bundletool-1.9.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1447914637817421545bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/bundletool/1.9.0/bundletool-1.9.0.pom, took 504 ms
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/jetifier/jetifier-core/1.0.0-beta10/jetifier-core-1.0.0-beta10.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/jetifier/jetifier-processor/1.0.0-beta10/jetifier-processor-1.0.0-beta10.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-core/1.0.0-beta10/jetifier-core-1.0.0-beta10.pom, took 325 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-core/1.0.0-beta10/jetifier-core-1.0.0-beta10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11407571986898826587bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-core/1.0.0-beta10/jetifier-core-1.0.0-beta10.module, took 325 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-core/1.0.0-beta10/jetifier-core-1.0.0-beta10.module to C:\Users\<USER>\.gradle\.tmp\gradle_download12242489566709196081bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-processor/1.0.0-beta10/jetifier-processor-1.0.0-beta10.pom, took 1 s 129 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javapoet/1.10.0/javapoet-1.10.0.pom, took 362 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-processor/1.0.0-beta10/jetifier-processor-1.0.0-beta10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3886932338539599756bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javapoet/1.10.0/javapoet-1.10.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2938001826263357620bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-processor/1.0.0-beta10/jetifier-processor-1.0.0-beta10.module to C:\Users\<USER>\.gradle\.tmp\gradle_download11555622586303629697bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-processor/1.0.0-beta10/jetifier-processor-1.0.0-beta10.module, took 521 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java/3.17.2/protobuf-java-3.17.2.pom, took 1 s 777 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java/3.17.2/protobuf-java-3.17.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16812748199088242331bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java-util/3.17.2/protobuf-java-util-3.17.2.pom, took 1 s 653 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java-util/3.17.2/protobuf-java-util-3.17.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2909353659176312437bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-parent/3.17.2/protobuf-parent-3.17.2.pom, took 1 s 925 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-parent/3.17.2/protobuf-parent-3.17.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12258067879980893835bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-bom/3.17.2/protobuf-bom-3.17.2.pom, took 343 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-bom/3.17.2/protobuf-bom-3.17.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download18104684169979869330bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson/2.8.6/gson-2.8.6.pom (2.53 kB / 2.53 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson/2.8.6/gson-2.8.6.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14013224076676967559bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson/2.8.6/gson-2.8.6.pom, took 338 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-core/1.39.0/grpc-core-1.39.0.pom, took 343 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-core/1.39.0/grpc-core-1.39.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2676678138544027214bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson-parent/2.8.6/gson-parent-2.8.6.pom (4.42 kB / 4.42 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson-parent/2.8.6/gson-parent-2.8.6.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13409075364970895236bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson-parent/2.8.6/gson-parent-2.8.6.pom, took 557 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-netty/1.39.0/grpc-netty-1.39.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7129600432045422046bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-netty/1.39.0/grpc-netty-1.39.0.pom, took 493 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf/1.39.0/grpc-protobuf-1.39.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13659385000025876659bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf/1.39.0/grpc-protobuf-1.39.0.pom, took 345 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-stub/1.39.0/grpc-stub-1.39.0.pom, took 363 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-stub/1.39.0/grpc-stub-1.39.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10498565528953979033bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/crypto/tink/tink/1.3.0-rc2/tink-1.3.0-rc2.pom, took 325 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/crypto/tink/tink/1.3.0-rc2/tink-1.3.0-rc2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1276807323192811058bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/flatbuffers/flatbuffers-java/1.12.0/flatbuffers-java-1.12.0.pom (4.57 kB / 4.57 kB)Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/google/testing/platform/core-proto/0.0.8-alpha07/core-proto-0.0.8-alpha07.pom]
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/flatbuffers/flatbuffers-java/1.12.0/flatbuffers-java-1.12.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17064828670035823178bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/flatbuffers/flatbuffers-java/1.12.0/flatbuffers-java-1.12.0.pom, took 390 ms
Downloading https://maven.aliyun.com/repository/google/com/google/testing/platform/core-proto/0.0.8-alpha07/core-proto-0.0.8-alpha07.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5689915108929614001bin
Download https://maven.aliyun.com/repository/google/com/google/testing/platform/core-proto/0.0.8-alpha07/core-proto-0.0.8-alpha07.pom, took 1 s 231 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/tensorflow/tensorflow-lite-metadata/0.1.0-rc2/tensorflow-lite-metadata-0.1.0-rc2.pom, took 361 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/tensorflow/tensorflow-lite-metadata/0.1.0-rc2/tensorflow-lite-metadata-0.1.0-rc2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15928199995595834739bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/builder/7.3.1/builder-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder/7.3.1/builder-7.3.1.pom, took 354 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder/7.3.1/builder-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6349251793702850356bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/builder-model/7.3.1/builder-model-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder/7.3.1/builder-7.3.1.module, took 336 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder/7.3.1/builder-7.3.1.module to C:\Users\<USER>\.gradle\.tmp\gradle_download475046284002640673bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder-model/7.3.1/builder-model-7.3.1.pom, took 330 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder-model/7.3.1/builder-model-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10830882933663562226bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder-model/7.3.1/builder-model-7.3.1.module, took 336 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder-model/7.3.1/builder-model-7.3.1.module to C:\Users\<USER>\.gradle\.tmp\gradle_download11244041497720226295bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/ddms/ddmlib/30.3.1/ddmlib-30.3.1.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/gradle-api/7.3.1/gradle-api-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/ddms/ddmlib/30.3.1/ddmlib-30.3.1.pom, took 340 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/ddms/ddmlib/30.3.1/ddmlib-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10836902038741870505bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/gradle-api/7.3.1/gradle-api-7.3.1.pom, took 350 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle-api/7.3.1/gradle-api-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3645692891186222162bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/gradle-api/7.3.1/gradle-api-7.3.1.module, took 337 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle-api/7.3.1/gradle-api-7.3.1.module to C:\Users\<USER>\.gradle\.tmp\gradle_download3105808158751109513bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/utp/android-test-plugin-result-listener-gradle-proto/30.3.1/android-test-plugin-result-listener-gradle-proto-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.pom, took 336 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1566202951973900771bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-result-listener-gradle-proto/30.3.1/android-test-plugin-result-listener-gradle-proto-30.3.1.pom, took 310 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-result-listener-gradle-proto/30.3.1/android-test-plugin-result-listener-gradle-proto-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12464997269388815462bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.module, took 332 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.module to C:\Users\<USER>\.gradle\.tmp\gradle_download3779534848362460029bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/utp/android-test-plugin-host-retention-proto/30.3.1/android-test-plugin-host-retention-proto-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.pom, took 914 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16013019112746010909bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-retention-proto/30.3.1/android-test-plugin-host-retention-proto-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4971336532647216667bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-retention-proto/30.3.1/android-test-plugin-host-retention-proto-30.3.1.pom, took 563 ms
Download https://maven.aliyun.com/repository/google/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.module, took 307 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.module to C:\Users\<USER>\.gradle\.tmp\gradle_download4391018712993461474bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/analytics-library/crash/30.3.1/crash-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/crash/30.3.1/crash-30.3.1.pom, took 353 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/crash/30.3.1/crash-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6943691207729845018bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/utp/android-test-plugin-host-coverage-proto/30.3.1/android-test-plugin-host-coverage-proto-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-coverage-proto/30.3.1/android-test-plugin-host-coverage-proto-30.3.1.pom, took 385 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-coverage-proto/30.3.1/android-test-plugin-host-coverage-proto-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7452149899486856645bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/analytics-library/shared/30.3.1/shared-30.3.1.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/utp/android-test-plugin-host-additional-test-output-proto/30.3.1/android-test-plugin-host-additional-test-output-proto-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/shared/30.3.1/shared-30.3.1.pom, took 372 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/shared/30.3.1/shared-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9021918941292162041bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-additional-test-output-proto/30.3.1/android-test-plugin-host-additional-test-output-proto-30.3.1.pom, took 381 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-additional-test-output-proto/30.3.1/android-test-plugin-host-additional-test-output-proto-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6944637301560244708bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/lint/lint-model/30.3.1/lint-model-30.3.1.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/utp/android-device-provider-gradle-proto/30.3.1/android-device-provider-gradle-proto-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-model/30.3.1/lint-model-30.3.1.pom, took 333 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-model/30.3.1/lint-model-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6261536279807880401bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-gradle-proto/30.3.1/android-device-provider-gradle-proto-30.3.1.pom, took 338 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-gradle-proto/30.3.1/android-device-provider-gradle-proto-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11309771815542934399bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/lint/lint-typedef-remover/30.3.1/lint-typedef-remover-30.3.1.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/utp/android-device-provider-ddmlib-proto/30.3.1/android-device-provider-ddmlib-proto-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-typedef-remover/30.3.1/lint-typedef-remover-30.3.1.pom, took 322 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-typedef-remover/30.3.1/lint-typedef-remover-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12327808094105359109bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-ddmlib-proto/30.3.1/android-device-provider-ddmlib-proto-30.3.1.pom (1.21 kB / 1.21 kB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-ddmlib-proto/30.3.1/android-device-provider-ddmlib-proto-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download569470485470906560bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-ddmlib-proto/30.3.1/android-device-provider-ddmlib-proto-30.3.1.pom, took 532 ms
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/layoutlib/layoutlib-api/30.3.1/layoutlib-api-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/layoutlib/layoutlib-api/30.3.1/layoutlib-api-30.3.1.pom, took 322 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/layoutlib/layoutlib-api/30.3.1/layoutlib-api-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16758187217221686076bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/androidx/databinding/databinding-compiler-common/7.3.1/databinding-compiler-common-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/androidx/databinding/databinding-compiler-common/7.3.1/databinding-compiler-common-7.3.1.pom, took 342 ms
Downloading https://maven.aliyun.com/repository/google/androidx/databinding/databinding-compiler-common/7.3.1/databinding-compiler-common-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6653183390887861562bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/androidx/databinding/databinding-common/7.3.1/databinding-common-7.3.1.pom]
Downloading https://maven.aliyun.com/repository/google/androidx/databinding/databinding-common/7.3.1/databinding-common-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13326588778458288543bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/databinding/baseLibrary/7.3.1/baseLibrary-7.3.1.pom]
Downloading https://maven.aliyun.com/repository/google/com/android/databinding/baseLibrary/7.3.1/baseLibrary-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11807993831086771070bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/builder-test-api/7.3.1/builder-test-api-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder-test-api/7.3.1/builder-test-api-7.3.1.pom, took 362 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder-test-api/7.3.1/builder-test-api-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1949653042887921857bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder-test-api/7.3.1/builder-test-api-7.3.1.module, took 313 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder-test-api/7.3.1/builder-test-api-7.3.1.module to C:\Users\<USER>\.gradle\.tmp\gradle_download5671922356958660283bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.7.10/kotlin-stdlib-jdk8-1.7.10.pom, took 480 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.7.10/kotlin-stdlib-jdk8-1.7.10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1389438178606865580bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20.pom, took 2 s 659 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13107427531136181549bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20.module to C:\Users\<USER>\.gradle\.tmp\gradle_download9493053902733516870bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20.module, took 3 s 181 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-impl-embeddable/1.8.20/kotlin-scripting-compiler-impl-embeddable-1.8.20.pom, took 2 s 507 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-impl-embeddable/1.8.20/kotlin-scripting-compiler-impl-embeddable-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15095596507752576457bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-model/1.8.20/kotlin-gradle-plugin-model-1.8.20.pom, took 2 s 554 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-model/1.8.20/kotlin-gradle-plugin-model-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11977328400442854806bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-tooling-core/1.8.20/kotlin-tooling-core-1.8.20.pom, took 2 s 434 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-tooling-core/1.8.20/kotlin-tooling-core-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4737050703593908185bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-model/1.8.20/kotlin-gradle-plugin-model-1.8.20.module (31.86 kB / 41.59 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-model/1.8.20/kotlin-gradle-plugin-model-1.8.20.module to C:\Users\<USER>\.gradle\.tmp\gradle_download5334870487699793620bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-model/1.8.20/kotlin-gradle-plugin-model-1.8.20.module, took 3 s 124 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea/1.8.20/kotlin-gradle-plugin-idea-1.8.20.pom, took 1 s 721 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea/1.8.20/kotlin-gradle-plugin-idea-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13875066871927224931bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-embeddable/1.8.20/kotlin-scripting-compiler-embeddable-1.8.20.pom, took 4 s 282 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-embeddable/1.8.20/kotlin-scripting-compiler-embeddable-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17247658109132264674bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea/1.8.20/kotlin-gradle-plugin-idea-1.8.20.module, took 3 s 179 ms

Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-runner/1.8.20/kotlin-compiler-runner-1.8.20.pom, took 2 s 563 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-runner/1.8.20/kotlin-compiler-runner-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3062785690122185928bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea-proto/1.8.20/kotlin-gradle-plugin-idea-proto-1.8.20.pom, took 3 s 159 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea-proto/1.8.20/kotlin-gradle-plugin-idea-proto-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16422096043591641873bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-android-extensions/1.8.20/kotlin-android-extensions-1.8.20.pom, took 3 s 706 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-android-extensions/1.8.20/kotlin-android-extensions-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13759735647445124739bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-klib/1.8.20/kotlin-util-klib-1.8.20.pom, took 1 s 857 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-klib/1.8.20/kotlin-util-klib-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17091429022023304027bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-klib-commonizer-api/1.8.20/kotlin-klib-commonizer-api-1.8.20.pom, took 1 s 821 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-klib-commonizer-api/1.8.20/kotlin-klib-commonizer-api-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15033654030263666669bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-annotation-processing-gradle/1.8.20/kotlin-annotation-processing-gradle-1.8.20.pom, took 3 s 400 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-annotation-processing-gradle/1.8.20/kotlin-annotation-processing-gradle-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9887846438183004154bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-project-model/1.8.20/kotlin-project-model-1.8.20.pom, took 4 s 53 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-project-model/1.8.20/kotlin-project-model-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14124665414676329415bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-embeddable/1.8.20/kotlin-compiler-embeddable-1.8.20.pom, took 7 s 697 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-embeddable/1.8.20/kotlin-compiler-embeddable-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2975057609712097326bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava/30.1-jre/guava-30.1-jre.pom, took 953 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava/30.1-jre/guava-30.1-jre.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2417115604881252898bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/common/30.3.1/common-30.3.1.pom]
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava-parent/30.1-jre/guava-parent-30.1-jre.pom (3.39 kB / 13.54 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava-parent/30.1-jre/guava-parent-30.1-jre.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12784830945663486953bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava-parent/30.1-jre/guava-parent-30.1-jre.pom, took 524 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/javax/inject/javax.inject/1/javax.inject-1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16094598477632625681bin
Download https://maven.aliyun.com/repository/google/com/android/tools/common/30.3.1/common-30.3.1.pom, took 1 s 389 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/common/30.3.1/common-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14383307375842978345bin
Download https://maven.aliyun.com/repository/gradle-plugin/xerces/xercesImpl/2.12.0/xercesImpl-2.12.0.pom, took 312 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/xerces/xercesImpl/2.12.0/xercesImpl-2.12.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download18106495390100540745bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/intellij/deps/trove4j/1.0.20181211/trove4j-1.0.20181211.pom, took 347 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/sf/kxml/kxml2/2.3.0/kxml2-2.3.0.pom (1.49 kB / 1.49 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/intellij/deps/trove4j/1.0.20181211/trove4j-1.0.20181211.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15683717636492791793bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/sf/kxml/kxml2/2.3.0/kxml2-2.3.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16602665409143432701bin
Download https://maven.aliyun.com/repository/gradle-plugin/net/sf/kxml/kxml2/2.3.0/kxml2-2.3.0.pom, took 375 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcprov-jdk15on/1.67/bcprov-jdk15on-1.67.pom, took 344 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcprov-jdk15on/1.67/bcprov-jdk15on-1.67.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5896582172387037450bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-reflect/1.7.10/kotlin-reflect-1.7.10.pom, took 315 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-reflect/1.7.10/kotlin-reflect-1.7.10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3773386536207541229bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-compress/1.20/commons-compress-1.20.pom, took 641 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-compress/1.20/commons-compress-1.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7724582566627588164bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/dvlib/30.3.1/dvlib-30.3.1.pom]
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/48/commons-parent-48.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download21662627158168048bin
Download https://maven.aliyun.com/repository/google/com/android/tools/dvlib/30.3.1/dvlib-30.3.1.pom, took 348 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/dvlib/30.3.1/dvlib-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16448426558053416785bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/48/commons-parent-48.pom, took 820 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9476049333638295821bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.pom, took 364 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/21/apache-21.pom, took 313 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/21/apache-21.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10808775384167637186bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-core/4.4.13/httpcomponents-core-4.4.13.pom, took 551 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-core/4.4.13/httpcomponents-core-4.4.13.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10493826880442154996bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-parent/11/httpcomponents-parent-11.pom (10.22 kB / 34.70 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-parent/11/httpcomponents-parent-11.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4864263939033375892bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-parent/11/httpcomponents-parent-11.pom, took 602 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/javax.activation/1.2.0/javax.activation-1.2.0.pom, took 317 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/jimfs/jimfs/1.1/jimfs-1.1.pom, took 335 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/javax.activation/1.2.0/javax.activation-1.2.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1453841294172856539bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/jimfs/jimfs/1.1/jimfs-1.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8794180060330537308bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/jimfs/jimfs-parent/1.1/jimfs-parent-1.1.pom, took 300 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/all/1.2.0/all-1.2.0.pom (7.32 kB / 17.95 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/jimfs/jimfs-parent/1.1/jimfs-parent-1.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7094104835141185293bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/all/1.2.0/all-1.2.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11580669488212645789bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/all/1.2.0/all-1.2.0.pom, took 515 ms
Download https://maven.aliyun.com/repository/gradle-plugin/net/java/jvnet-parent/1/jvnet-parent-1.pom, took 285 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/jvnet-parent/1/jvnet-parent-1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5716229323223476474bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.pom, took 319 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download305604739553296383bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-client/4.5.13/httpcomponents-client-4.5.13.pom, took 323 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcomponents-client/4.5.13/httpcomponents-client-4.5.13.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6236352860180316180bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/annotations/30.3.1/annotations-30.3.1.pom]
Downloading https://maven.aliyun.com/repository/google/com/android/tools/annotations/30.3.1/annotations-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6157650938536912566bin
Download https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna-platform/5.6.0/jna-platform-5.6.0.pom, took 380 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna-platform/5.6.0/jna-platform-5.6.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4462675003030300665bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/analytics-library/protos/30.3.1/protos-30.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/protos/30.3.1/protos-30.3.1.pom, took 326 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/protos/30.3.1/protos-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10654134303884367030bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/juniversalchardet/juniversalchardet/1.0.3/juniversalchardet-1.0.3.pom, took 297 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/juniversalchardet/juniversalchardet/1.0.3/juniversalchardet-1.0.3.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8033592543567957376bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/annotations/13.0/annotations-13.0.pom, took 284 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/annotations/13.0/annotations-13.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15496413269279736237bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.pom (2.92 kB / 14.48 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14484568939459924869bin
Download https://maven.aliyun.com/repository/gradle-plugin/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.pom, took 586 ms
Download https://maven.aliyun.com/repository/gradle-plugin/net/java/jvnet-parent/3/jvnet-parent-3.pom, took 284 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/jvnet-parent/3/jvnet-parent-3.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7625687451918483676bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-tree/9.1/asm-tree-9.1.pom, took 357 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-tree/9.1/asm-tree-9.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1277642123034627078bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/txw2/2.3.2/txw2-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4143955447667754727bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/txw2/2.3.2/txw2-2.3.2.pom, took 494 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/jakarta/xml/bind/jakarta.xml.bind-api/2.3.2/jakarta.xml.bind-api-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13070788320061489192bin
Download https://maven.aliyun.com/repository/gradle-plugin/jakarta/xml/bind/jakarta.xml.bind-api/2.3.2/jakarta.xml.bind-api-2.3.2.pom, took 587 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/mvn/jaxb-txw-parent/2.3.2/jaxb-txw-parent-2.3.2.pom, took 310 ms
Download https://maven.aliyun.com/repository/gradle-plugin/jakarta/xml/bind/jakarta.xml.bind-api-parent/2.3.2/jakarta.xml.bind-api-parent-2.3.2.pom, took 363 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/bind/mvn/jaxb-txw-parent/2.3.2/jaxb-txw-parent-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8925940236310929852bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/jakarta/xml/bind/jakarta.xml.bind-api-parent/2.3.2/jakarta.xml.bind-api-parent-2.3.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2351261931308759449bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/istack/istack-commons-runtime/3.0.8/istack-commons-runtime-3.0.8.pom, took 331 ms
Download https://maven.aliyun.com/repository/gradle-plugin/jakarta/activation/jakarta.activation-api/1.2.1/jakarta.activation-api-1.2.1.pom, took 366 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/istack/istack-commons-runtime/3.0.8/istack-commons-runtime-3.0.8.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5150407664700150579bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/jakarta/activation/jakarta.activation-api/1.2.1/jakarta.activation-api-1.2.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8816924093479069603bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/istack/istack-commons/3.0.8/istack-commons-3.0.8.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9561088641689763585bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/all/1.2.1/all-1.2.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2779749602560490605bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/istack/istack-commons/3.0.8/istack-commons-3.0.8.pom, took 534 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/all/1.2.1/all-1.2.1.pom, took 598 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/eclipse/ee4j/project/1.0.2/project-1.0.2.pom, took 350 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/eclipse/ee4j/project/1.0.2/project-1.0.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15270089338888447807bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/fastinfoset/FastInfoset/1.2.16/FastInfoset-1.2.16.pom, took 339 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/fastinfoset/FastInfoset/1.2.16/FastInfoset-1.2.16.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15049252021871374712bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/fastinfoset/fastinfoset-project/1.2.16/fastinfoset-project-1.2.16.pom (9.24 kB / 15.18 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/fastinfoset/fastinfoset-project/1.2.16/fastinfoset-project-1.2.16.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17995363301255632068bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/fastinfoset/fastinfoset-project/1.2.16/fastinfoset-project-1.2.16.pom, took 534 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jvnet/staxex/stax-ex/1.8.1/stax-ex-1.8.1.pom (10.22 kB / 15.15 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jvnet/staxex/stax-ex/1.8.1/stax-ex-1.8.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11378441063879031120bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jvnet/staxex/stax-ex/1.8.1/stax-ex-1.8.1.pom, took 576 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.3.1/error_prone_annotations-2.3.1.pom, took 274 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.3.1/error_prone_annotations-2.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14955407846147792384bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/value/auto-value-annotations/1.6.2/auto-value-annotations-1.6.2.pom, took 322 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/value/auto-value-annotations/1.6.2/auto-value-annotations-1.6.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15228666490996811351bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_parent/2.3.1/error_prone_parent-2.3.1.pom, took 325 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_parent/2.3.1/error_prone_parent-2.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download18379355184448082707bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/value/auto-value-parent/1.6.2/auto-value-parent-1.6.2.pom, took 453 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/value/auto-value-parent/1.6.2/auto-value-parent-1.6.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3355633679640061129bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/auto-parent/6/auto-parent-6.pom, took 277 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/auto-parent/6/auto-parent-6.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3296839991582941556bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/dagger/dagger/2.28.3/dagger-2.28.3.pom, took 318 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/dagger/dagger/2.28.3/dagger-2.28.3.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14150659931598229539bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.pom, took 308 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2476987704411144920bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/slf4j/slf4j-parent/1.7.30/slf4j-parent-1.7.30.pom, took 612 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/slf4j/slf4j-parent/1.7.30/slf4j-parent-1.7.30.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2136160148779976332bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bitbucket/b_c/jose4j/0.7.0/jose4j-0.7.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2709059442183536594bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/bitbucket/b_c/jose4j/0.7.0/jose4j-0.7.0.pom, took 2 s 124 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib/1.7.10/kotlin-stdlib-1.7.10.pom, took 2 s 291 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib/1.7.10/kotlin-stdlib-1.7.10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download379479051365435562bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jdom/jdom2/2.0.6/jdom2-2.0.6.pom, took 341 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jdom/jdom2/2.0.6/jdom2-2.0.6.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4241103995515150711bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.3.4/error_prone_annotations-2.3.4.pom, took 327 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.3.4/error_prone_annotations-2.3.4.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15324423921335187260bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_parent/2.3.4/error_prone_parent-2.3.4.pom, took 335 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_parent/2.3.4/error_prone_parent-2.3.4.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2953854902256267597bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0.pom, took 338 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17182578021827118564bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_parent/2.4.0/error_prone_parent-2.4.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13816679064357418079bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_parent/2.4.0/error_prone_parent-2.4.0.pom, took 2 s 288 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.pom, took 384 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-api/1.39.0/grpc-api-1.39.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11127223560971333175bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1192086555010845130bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-api/1.39.0/grpc-api-1.39.0.pom, took 440 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/animal-sniffer-annotations/1.19/animal-sniffer-annotations-1.19.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download30646002057052302bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/perfmark/perfmark-api/0.23.0/perfmark-api-0.23.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14425161836887645716bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/perfmark/perfmark-api/0.23.0/perfmark-api-0.23.0.pom, took 449 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/animal-sniffer-annotations/1.19/animal-sniffer-annotations-1.19.pom, took 465 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/perfmark/perfmark-api/0.23.0/perfmark-api-0.23.0.module, took 335 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/perfmark/perfmark-api/0.23.0/perfmark-api-0.23.0.module to C:\Users\<USER>\.gradle\.tmp\gradle_download14174459012474987238bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/animal-sniffer-parent/1.19/animal-sniffer-parent-1.19.pom, took 2 s 510 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/animal-sniffer-parent/1.19/animal-sniffer-parent-1.19.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12708778963739014914bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/mojo-parent/50/mojo-parent-50.pom, took 671 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/mojo-parent/50/mojo-parent-50.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8505217050964674088bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http2/4.1.52.Final/netty-codec-http2-4.1.52.Final.pom, took 343 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http2/4.1.52.Final/netty-codec-http2-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download15628257397632548450bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler-proxy/4.1.52.Final/netty-handler-proxy-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11769021839865437401bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler-proxy/4.1.52.Final/netty-handler-proxy-4.1.52.Final.pom, took 1 s 88 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-parent/4.1.52.Final/netty-parent-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17258966573833438395bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-parent/4.1.52.Final/netty-parent-4.1.52.Final.pom, took 1 s 62 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/sonatype/oss/oss-parent/9/oss-parent-9.pom, took 302 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/sonatype/oss/oss-parent/9/oss-parent-9.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11614493111554072706bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.pom, took 330 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2791691129090773403bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf-lite/1.39.0/grpc-protobuf-lite-1.39.0.pom, took 362 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf-lite/1.39.0/grpc-protobuf-lite-1.39.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2297138178884014605bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/api/grpc/proto-google-common-protos/2.0.1/proto-google-common-protos-2.0.1.pom, took 1 s 665 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/api/grpc/proto-google-common-protos/2.0.1/proto-google-common-protos-2.0.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8929476254497093690bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/json/json/20180813/json-20180813.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3750829744776480070bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/json/json/20180813/json-20180813.pom, took 560 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/checkerframework/checker-qual/2.5.8/checker-qual-2.5.8.pom, took 336 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/checkerframework/checker-qual/2.5.8/checker-qual-2.5.8.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14232820415211657624bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/analytics-library/tracker/30.3.1/tracker-30.3.1.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/signflinger/7.3.1/signflinger-7.3.1.pom]
Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/tracker/30.3.1/tracker-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6937888062472429703bin
Download https://maven.aliyun.com/repository/google/com/android/signflinger/7.3.1/signflinger-7.3.1.pom, took 1 s 84 ms
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/tracker/30.3.1/tracker-30.3.1.pom, took 1 s 196 ms
Downloading https://maven.aliyun.com/repository/google/com/android/signflinger/7.3.1/signflinger-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4496388245212809032bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javawriter/2.5.0/javawriter-2.5.0.pom, took 306 ms
Download https://maven.aliyun.com/repository/gradle-plugin/commons-codec/commons-codec/1.10/commons-codec-1.10.pom, took 307 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-codec/commons-codec/1.10/commons-codec-1.10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download17246574680338336678bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javawriter/2.5.0/javawriter-2.5.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8489254941436157796bin
Download https://maven.aliyun.com/repository/gradle-plugin/it/unimi/dsi/fastutil/8.4.0/fastutil-8.4.0.pom, took 340 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/it/unimi/dsi/fastutil/8.4.0/fastutil-8.4.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7333670561456228521bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/35/commons-parent-35.pom (31.78 kB / 57.77 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/35/commons-parent-35.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7770891084023152690bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/35/commons-parent-35.pom, took 768 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/15/apache-15.pom, took 290 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/15/apache-15.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12928791157769811964bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/apkzlib/7.3.1/apkzlib-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/apkzlib/7.3.1/apkzlib-7.3.1.pom, took 325 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/apkzlib/7.3.1/apkzlib-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5096639945946823638bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/json-simple/json-simple/1.1/json-simple-1.1.pom, took 288 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/json-simple/json-simple/1.1/json-simple-1.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download12006536265007853460bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/manifest-merger/30.3.1/manifest-merger-30.3.1.pom]
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/zipflinger/7.3.1/zipflinger-7.3.1.pom]
Download https://maven.aliyun.com/repository/google/com/android/tools/build/manifest-merger/30.3.1/manifest-merger-30.3.1.pom, took 330 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/manifest-merger/30.3.1/manifest-merger-30.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8290954440054838495bin
Download https://maven.aliyun.com/repository/google/com/android/zipflinger/7.3.1/zipflinger-7.3.1.pom, took 314 ms
Downloading https://maven.aliyun.com/repository/google/com/android/zipflinger/7.3.1/zipflinger-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download2195225120816276144bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/manifest-merger/30.3.1/manifest-merger-30.3.1.module, took 349 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/manifest-merger/30.3.1/manifest-merger-30.3.1.module to C:\Users\<USER>\.gradle\.tmp\gradle_download6248696564381088607bin
Resource missing. [HTTP GET: https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/apksig/7.3.1/apksig-7.3.1.pom]
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/apksig/7.3.1/apksig-7.3.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download5357305434313150956bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.7.10/kotlin-stdlib-jdk7-1.7.10.pom, took 1 s 704 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.7.10/kotlin-stdlib-jdk7-1.7.10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11166364429064487318bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-native-utils/1.8.20/kotlin-native-utils-1.8.20.pom, took 1 s 151 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-native-utils/1.8.20/kotlin-native-utils-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1419744179096111393bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-annotations/1.8.20/kotlin-gradle-plugin-annotations-1.8.20.pom, took 1 s 736 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-annotations/1.8.20/kotlin-gradle-plugin-annotations-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download6253464080054718417bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-io/1.8.20/kotlin-util-io-1.8.20.pom, took 1 s 839 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-io/1.8.20/kotlin-util-io-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14143480104336280994bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/intellij/deps/trove4j/1.0.20200330/trove4j-1.0.20200330.pom, took 373 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/intellij/deps/trove4j/1.0.20200330/trove4j-1.0.20200330.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download805446494825002957bin
Download https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna/5.6.0/jna-5.6.0.pom, took 334 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna/5.6.0/jna-5.6.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download7490538746488063186bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-embeddable/1.8.20/kotlin-daemon-embeddable-1.8.20.pom, took 1 s 729 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-embeddable/1.8.20/kotlin-daemon-embeddable-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14628347411629003859bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlinx/kotlinx-coroutines-core-jvm/1.5.0/kotlinx-coroutines-core-jvm-1.5.0.pom, took 372 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlinx/kotlinx-coroutines-core-jvm/1.5.0/kotlinx-coroutines-core-jvm-1.5.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13411518106322169911bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlinx/kotlinx-coroutines-core-jvm/1.5.0/kotlinx-coroutines-core-jvm-1.5.0.module, took 344 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlinx/kotlinx-coroutines-core-jvm/1.5.0/kotlinx-coroutines-core-jvm-1.5.0.module to C:\Users\<USER>\.gradle\.tmp\gradle_download9484953308063902634bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-client/1.8.20/kotlin-daemon-client-1.8.20.pom, took 2 s 544 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-client/1.8.20/kotlin-daemon-client-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download379608980047559269bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-common/1.8.20/kotlin-scripting-common-1.8.20.pom, took 1 s 789 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-common/1.8.20/kotlin-scripting-common-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9676959997202552553bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-jvm/1.8.20/kotlin-scripting-jvm-1.8.20.pom, took 1 s 876 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-jvm/1.8.20/kotlin-scripting-jvm-1.8.20.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11773755565370324709bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4263477500914347464bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.pom, took 1 s 26 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.pom, took 1 s 189 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.pom (2.41 kB / 2.41 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3372966619211199905bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11550504002640113866bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.pom, took 1 s 245 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava-parent/26.0-android/guava-parent-26.0-android.pom, took 299 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava-parent/26.0-android/guava-parent-26.0-android.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16267931537371069802bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.pom, took 308 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download4783811255820803263bin
Download https://maven.aliyun.com/repository/gradle-plugin/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.pom, took 271 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8455237461196007126bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-codec/commons-codec/1.11/commons-codec-1.11.pom (13.97 kB / 13.97 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-codec/commons-codec/1.11/commons-codec-1.11.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download320599409372347563bin
Download https://maven.aliyun.com/repository/gradle-plugin/commons-codec/commons-codec/1.11/commons-codec-1.11.pom, took 713 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/42/commons-parent-42.pom (18.32 kB / 67.96 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/42/commons-parent-42.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download11904759691244671646bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/42/commons-parent-42.pom, took 502 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-logging/commons-logging/1.2/commons-logging-1.2.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download364285472845146358bin
Download https://maven.aliyun.com/repository/gradle-plugin/commons-logging/commons-logging/1.2/commons-logging-1.2.pom, took 363 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/34/commons-parent-34.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9926641961710287102bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-parent/34/commons-parent-34.pom, took 657 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/13/apache-13.pom, took 302 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/apache/13/apache-13.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1211995277285719932bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-common/1.7.10/kotlin-stdlib-common-1.7.10.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download9311207751322954593bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-common/1.7.10/kotlin-stdlib-common-1.7.10.pom, took 337 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-context/1.39.0/grpc-context-1.39.0.pom, took 409 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-context/1.39.0/grpc-context-1.39.0.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1553924040183536881bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-buffer/4.1.52.Final/netty-buffer-4.1.52.Final.pom, took 1 s 501 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-buffer/4.1.52.Final/netty-buffer-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download8360357468511824223bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-common/4.1.52.Final/netty-common-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download10654310661747652663bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-common/4.1.52.Final/netty-common-4.1.52.Final.pom, took 2 s 251 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http/4.1.52.Final/netty-codec-http-4.1.52.Final.pom, took 353 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http/4.1.52.Final/netty-codec-http-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download3744078206428328120bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-transport/4.1.52.Final/netty-transport-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download13948837689636677277bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-transport/4.1.52.Final/netty-transport-4.1.52.Final.pom, took 2 s 232 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler/4.1.52.Final/netty-handler-4.1.52.Final.pom, took 447 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler/4.1.52.Final/netty-handler-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download1568980967454028717bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec/4.1.52.Final/netty-codec-4.1.52.Final.pom, took 2 s 249 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec/4.1.52.Final/netty-codec-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download14862570398773982657bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-socks/4.1.52.Final/netty-codec-socks-4.1.52.Final.pom, took 1 s 224 ms

Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-resolver/4.1.52.Final/netty-resolver-4.1.52.Final.pom (1.46 kB / 1.58 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-resolver/4.1.52.Final/netty-resolver-4.1.52.Final.pom to C:\Users\<USER>\.gradle\.tmp\gradle_download16819173459229726096bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-resolver/4.1.52.Final/netty-resolver-4.1.52.Final.pom, took 793 ms
Resource missing. [HTTP HEAD: https://maven.aliyun.com/repository/google/com/android/application/com.android.application.gradle.plugin/7.3.1/com.android.application.gradle.plugin-7.3.1.jar]
Resource missing. [HTTP HEAD: https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/android/org.jetbrains.kotlin.android.gradle.plugin/1.8.20/org.jetbrains.kotlin.android.gradle.plugin-1.8.20.jar]
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download17627350113486679621bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (86.85 kB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/androidx/databinding/databinding-compiler-common/7.3.1/databinding-compiler-common-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6014872025003042143bin
Download https://maven.aliyun.com/repository/google/androidx/databinding/databinding-compiler-common/7.3.1/databinding-compiler-common-7.3.1.jar, took 2 s 659 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (334.07 kB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.7.10/kotlin-stdlib-jdk8-1.7.10.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download14205998421542338687bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.7.10/kotlin-stdlib-jdk8-1.7.10.jar, took 599 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (418.91 kB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-reflect/1.7.10/kotlin-reflect-1.7.10.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5927258880771142996bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-reflect/1.7.10/kotlin-reflect-1.7.10.jar, took 3 s 83 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (683.42 kB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.7.10/kotlin-stdlib-jdk7-1.7.10.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9627656694175536727bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.7.10/kotlin-stdlib-jdk7-1.7.10.jar, took 445 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib/1.7.10/kotlin-stdlib-1.7.10.jar (31.77 kB / 1.52 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib/1.7.10/kotlin-stdlib-1.7.10.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15330213908168464115bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib/1.7.10/kotlin-stdlib-1.7.10.jar, took 1 s 807 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (1.01 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-common/1.7.10/kotlin-stdlib-common-1.7.10.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download14137567278253356497bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-stdlib-common/1.7.10/kotlin-stdlib-common-1.7.10.jar, took 5 s 677 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/annotations/13.0/annotations-13.0.jar, took 733 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/annotations/13.0/annotations-13.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download11641740834376941884bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-model/1.8.20/kotlin-gradle-plugin-model-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download8369277536692718563bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-model/1.8.20/kotlin-gradle-plugin-model-1.8.20.jar, took 6 s 78 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20-gradle76.jar (3.38 kB / 240.63 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20-gradle76.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9889762707742811306bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20-gradle76.jar, took 3 s 896 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20.jar (11.76 kB / 240.63 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9245632588686740035bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-api/1.8.20/kotlin-gradle-plugin-api-1.8.20.jar, took 3 s 278 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20-gradle76.jar (11.76 kB / 13.34 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20-gradle76.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6399412123364080953bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin/1.8.20/kotlin-gradle-plugin-1.8.20-gradle76.jar, took 10 s 872 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/bundletool/1.9.0/bundletool-1.9.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13967053746276175634bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/bundletool/1.9.0/bundletool-1.9.0.jar, took 5 s 554 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.jar (168.69 kB / 749.37 kB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9172075072530525402bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/aapt2-proto/7.3.1-8691043/aapt2-proto-7.3.1-8691043.jar, took 3 s 836 ms
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/crash/30.3.1/crash-30.3.1.jar, took 347 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/crash/30.3.1/crash-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download14019472441449263155bin
Download https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-typedef-remover/30.3.1/lint-typedef-remover-30.3.1.jar, took 326 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-typedef-remover/30.3.1/lint-typedef-remover-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download11701519133758958894bin
Download https://maven.aliyun.com/repository/google/androidx/databinding/databinding-common/7.3.1/databinding-common-7.3.1.jar, took 326 ms
Downloading https://maven.aliyun.com/repository/google/androidx/databinding/databinding-common/7.3.1/databinding-common-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download12034265337156505756bin
Downloading https://maven.aliyun.com/repository/google/com/android/databinding/baseLibrary/7.3.1/baseLibrary-7.3.1.jar (16.73 kB / 16.73 kB)Downloading https://maven.aliyun.com/repository/google/com/android/databinding/baseLibrary/7.3.1/baseLibrary-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3331459632782235362bin
Download https://maven.aliyun.com/repository/google/com/android/databinding/baseLibrary/7.3.1/baseLibrary-7.3.1.jar, took 433 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-ddmlib-proto/30.3.1/android-device-provider-ddmlib-proto-30.3.1.jar (19.81 kB / 19.81 kB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-ddmlib-proto/30.3.1/android-device-provider-ddmlib-proto-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download4194346112357134803bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-ddmlib-proto/30.3.1/android-device-provider-ddmlib-proto-30.3.1.jar, took 332 ms
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-gradle-proto/30.3.1/android-device-provider-gradle-proto-30.3.1.jar, took 329 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-device-provider-gradle-proto/30.3.1/android-device-provider-gradle-proto-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15146310619416072622bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-additional-test-output-proto/30.3.1/android-test-plugin-host-additional-test-output-proto-30.3.1.jar, took 395 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-additional-test-output-proto/30.3.1/android-test-plugin-host-additional-test-output-proto-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6964455051168345092bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-coverage-proto/30.3.1/android-test-plugin-host-coverage-proto-30.3.1.jar, took 398 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-coverage-proto/30.3.1/android-test-plugin-host-coverage-proto-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1228167256435770699bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-result-listener-gradle-proto/30.3.1/android-test-plugin-result-listener-gradle-proto-30.3.1.jar (81.92 kB / 111.57 kB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-result-listener-gradle-proto/30.3.1/android-test-plugin-result-listener-gradle-proto-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10329674993104868763bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-result-listener-gradle-proto/30.3.1/android-test-plugin-result-listener-gradle-proto-30.3.1.jar, took 546 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/android/tools/build/transform-api/2.0.0-deprecated-use-gradle-api/transform-api-2.0.0-deprecated-use-gradle-api.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1884259267912777451bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpmime/4.5.6/httpmime-4.5.6.jar (1.93 kB / 41.79 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpmime/4.5.6/httpmime-4.5.6.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10972529289885910323bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpmime/4.5.6/httpmime-4.5.6.jar, took 1 s 149 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-io/commons-io/2.4/commons-io-2.4.jar (51.09 kB / 185.14 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-io/commons-io/2.4/commons-io-2.4.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10122208982976089669bin
Download https://maven.aliyun.com/repository/gradle-plugin/commons-io/commons-io/2.4/commons-io-2.4.jar, took 852 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-commons/9.1/asm-commons-9.1.jar, took 317 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-commons/9.1/asm-commons-9.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15878180113385142539bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-util/9.1/asm-util-9.1.jar, took 358 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-util/9.1/asm-util-9.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download8047382674863592803bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-analysis/9.1/asm-analysis-9.1.jar (18.31 kB / 34.26 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-analysis/9.1/asm-analysis-9.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download16409456072511963805bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-analysis/9.1/asm-analysis-9.1.jar, took 2 s 604 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-tree/9.1/asm-tree-9.1.jar (11.76 kB / 52.66 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-tree/9.1/asm-tree-9.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7359775002695329311bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm-tree/9.1/asm-tree-9.1.jar, took 2 s 267 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm/9.1/asm-9.1.jar (51.42 kB / 121.79 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm/9.1/asm-9.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download12752800275511825409bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/ow2/asm/asm/9.1/asm-9.1.jar, took 584 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/apkzlib/7.3.1/apkzlib-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9909027480818626297bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/apkzlib/7.3.1/apkzlib-7.3.1.jar, took 1 s 393 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcpkix-jdk15on/1.67/bcpkix-jdk15on-1.67.jar (16.38 kB / 887.81 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcpkix-jdk15on/1.67/bcpkix-jdk15on-1.67.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15905252592251781906bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcpkix-jdk15on/1.67/bcpkix-jdk15on-1.67.jar, took 1 s 587 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-runtime/2.3.2/jaxb-runtime-2.3.2.jar (290.53 kB / 1.01 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-runtime/2.3.2/jaxb-runtime-2.3.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15152205591281799047bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/jaxb-runtime/2.3.2/jaxb-runtime-2.3.2.jar, took 921 ms
Download https://maven.aliyun.com/repository/gradle-plugin/net/sf/jopt-simple/jopt-simple/4.9/jopt-simple-4.9.jar, took 310 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/sf/jopt-simple/jopt-simple/4.9/jopt-simple-4.9.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3794693616092452396bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javapoet/1.10.0/javapoet-1.10.0.jar, took 332 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javapoet/1.10.0/javapoet-1.10.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download650037013095573413bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java-util/3.17.2/protobuf-java-util-3.17.2.jar, took 349 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java-util/3.17.2/protobuf-java-util-3.17.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15346231501318808253bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf/1.39.0/grpc-protobuf-1.39.0.jar, took 1 s 969 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf/1.39.0/grpc-protobuf-1.39.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15683249264007763788bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/crypto/tink/tink/1.3.0-rc2/tink-1.3.0-rc2.jar (41.85 kB / 1.14 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/crypto/tink/tink/1.3.0-rc2/tink-1.3.0-rc2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download17586816756118928643bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/crypto/tink/tink/1.3.0-rc2/tink-1.3.0-rc2.jar, took 1 s 724 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/protos/30.3.1/protos-30.3.1.jar (31.78 kB / 5.75 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/protos/30.3.1/protos-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download12191915224427133278bin
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/protos/30.3.1/protos-30.3.1.jar, took 3 s 200 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (1.93 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/api/grpc/proto-google-common-protos/2.0.1/proto-google-common-protos-2.0.1.jar (2.92 kB / 1.56 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/api/grpc/proto-google-common-protos/2.0.1/proto-google-common-protos-2.0.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download67770590545931489bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/api/grpc/proto-google-common-protos/2.0.1/proto-google-common-protos-2.0.1.jar, took 1 s 987 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java/3.17.2/protobuf-java-3.17.2.jar (767.94 kB / 1.68 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java/3.17.2/protobuf-java-3.17.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download16981150209136569053bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/protobuf/protobuf-java/3.17.2/protobuf-java-3.17.2.jar, took 890 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-netty/1.39.0/grpc-netty-1.39.0.jar, took 331 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-netty/1.39.0/grpc-netty-1.39.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6031128836196900940bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-core/1.39.0/grpc-core-1.39.0.jar (589.82 kB / 655.88 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-core/1.39.0/grpc-core-1.39.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9847820115243948677bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-core/1.39.0/grpc-core-1.39.0.jar, took 3 s 285 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, took 326 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download8026546604340425027bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-stub/1.39.0/grpc-stub-1.39.0.jar, took 372 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-stub/1.39.0/grpc-stub-1.39.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1751434745925598700bin
Downloading https://maven.aliyun.com/repository/google/com/google/testing/platform/core-proto/0.0.8-alpha07/core-proto-0.0.8-alpha07.jar (1.92 kB / 1.01 MB)Downloading https://maven.aliyun.com/repository/google/com/google/testing/platform/core-proto/0.0.8-alpha07/core-proto-0.0.8-alpha07.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13261837113687124945bin
Download https://maven.aliyun.com/repository/google/com/google/testing/platform/core-proto/0.0.8-alpha07/core-proto-0.0.8-alpha07.jar, took 2 s 188 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/tensorflow/tensorflow-lite-metadata/0.1.0-rc2/tensorflow-lite-metadata-0.1.0-rc2.jar (1.91 kB / 348.39 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/tensorflow/tensorflow-lite-metadata/0.1.0-rc2/tensorflow-lite-metadata-0.1.0-rc2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1165530091726693566bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/tensorflow/tensorflow-lite-metadata/0.1.0-rc2/tensorflow-lite-metadata-0.1.0-rc2.jar, took 1 s 313 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/flatbuffers/flatbuffers-java/1.12.0/flatbuffers-java-1.12.0.jar, took 366 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/flatbuffers/flatbuffers-java/1.12.0/flatbuffers-java-1.12.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download2985372893882006923bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (1.98 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea-proto/1.8.20/kotlin-gradle-plugin-idea-proto-1.8.20.jar (7.66 kB / 2.88 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea-proto/1.8.20/kotlin-gradle-plugin-idea-proto-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5298592894205463583bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea-proto/1.8.20/kotlin-gradle-plugin-idea-proto-1.8.20.jar, took 5 s 174 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea/1.8.20/kotlin-gradle-plugin-idea-1.8.20.jar (28.14 kB / 168.27 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea/1.8.20/kotlin-gradle-plugin-idea-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download14026699732787262177bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-idea/1.8.20/kotlin-gradle-plugin-idea-1.8.20.jar, took 2 s 332 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-project-model/1.8.20/kotlin-project-model-1.8.20.jar (24.05 kB / 65.23 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-project-model/1.8.20/kotlin-project-model-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download8987271384253347066bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-project-model/1.8.20/kotlin-project-model-1.8.20.jar, took 2 s 61 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-tooling-core/1.8.20/kotlin-tooling-core-1.8.20.jar (11.76 kB / 71.66 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-tooling-core/1.8.20/kotlin-tooling-core-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6177436027446509257bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-tooling-core/1.8.20/kotlin-tooling-core-1.8.20.jar, took 3 s 113 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-klib/1.8.20/kotlin-util-klib-1.8.20.jar (11.76 kB / 268.46 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-klib/1.8.20/kotlin-util-klib-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9731377105494056325bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-klib/1.8.20/kotlin-util-klib-1.8.20.jar, took 3 s 15 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (2.19 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-klib-commonizer-api/1.8.20/kotlin-klib-commonizer-api-1.8.20.jar (7.66 kB / 79.67 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-klib-commonizer-api/1.8.20/kotlin-klib-commonizer-api-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download16525492762331752728bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-klib-commonizer-api/1.8.20/kotlin-klib-commonizer-api-1.8.20.jar, took 2 s 948 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-annotation-processing-gradle/1.8.20/kotlin-annotation-processing-gradle-1.8.20.jar (11.76 kB / 474.25 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-annotation-processing-gradle/1.8.20/kotlin-annotation-processing-gradle-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download8115785818619406607bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-annotation-processing-gradle/1.8.20/kotlin-annotation-processing-gradle-1.8.20.jar, took 2 s 973 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-android-extensions/1.8.20/kotlin-android-extensions-1.8.20.jar (7.66 kB / 495.86 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-android-extensions/1.8.20/kotlin-android-extensions-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download18824652296014609bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-android-extensions/1.8.20/kotlin-android-extensions-1.8.20.jar, took 3 s 684 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-runner/1.8.20/kotlin-compiler-runner-1.8.20.jar, took 1 s 689 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-runner/1.8.20/kotlin-compiler-runner-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9932834972667865349bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-embeddable/1.8.20/kotlin-compiler-embeddable-1.8.20.jar (11.76 kB / 55.37 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-embeddable/1.8.20/kotlin-compiler-embeddable-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10102353753223068332bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-compiler-embeddable/1.8.20/kotlin-compiler-embeddable-1.8.20.jar, took 27 s 190 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-embeddable/1.8.20/kotlin-scripting-compiler-embeddable-1.8.20.jar (40.43 kB / 427.24 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-embeddable/1.8.20/kotlin-scripting-compiler-embeddable-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1660568440687623740bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-embeddable/1.8.20/kotlin-scripting-compiler-embeddable-1.8.20.jar, took 2 s 518 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-impl-embeddable/1.8.20/kotlin-scripting-compiler-impl-embeddable-1.8.20.jar (24.04 kB / 320.15 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-impl-embeddable/1.8.20/kotlin-scripting-compiler-impl-embeddable-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download4716606915276878216bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-compiler-impl-embeddable/1.8.20/kotlin-scripting-compiler-impl-embeddable-1.8.20.jar, took 2 s 622 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/jimfs/jimfs/1.1/jimfs-1.1.jar, took 301 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/jimfs/jimfs/1.1/jimfs-1.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download4397475913180289738bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf-lite/1.39.0/grpc-protobuf-lite-1.39.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15673361075201973397bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-protobuf-lite/1.39.0/grpc-protobuf-lite-1.39.0.jar, took 1 s 631 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-api/1.39.0/grpc-api-1.39.0.jar (18.29 kB / 254.52 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-api/1.39.0/grpc-api-1.39.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9368039342302305465bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-api/1.39.0/grpc-api-1.39.0.jar, took 1 s 21 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar (272.50 kB / 2.86 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13539261454964410247bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar, took 1 s 131 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/dagger/dagger/2.28.3/dagger-2.28.3.jar, took 339 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/dagger/dagger/2.28.3/dagger-2.28.3.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6644966613232405780bin
Download https://maven.aliyun.com/repository/gradle-plugin/javax/inject/javax.inject/1/javax.inject-1.jar, took 292 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/javax/inject/javax.inject/1/javax.inject-1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7800004827190424899bin
Download https://maven.aliyun.com/repository/gradle-plugin/net/sf/kxml/kxml2/2.3.0/kxml2-2.3.0.jar, took 306 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/sf/kxml/kxml2/2.3.0/kxml2-2.3.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download16052824930002368139bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcprov-jdk15on/1.67/bcprov-jdk15on-1.67.jar (622.59 kB / 5.96 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcprov-jdk15on/1.67/bcprov-jdk15on-1.67.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10672041449751171756bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/bouncycastle/bcprov-jdk15on/1.67/bcprov-jdk15on-1.67.jar, took 2 s 468 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/intellij/deps/trove4j/1.0.20200330/trove4j-1.0.20200330.jar (67.44 kB / 572.99 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/intellij/deps/trove4j/1.0.20200330/trove4j-1.0.20200330.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3033513860329255234bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/intellij/deps/trove4j/1.0.20200330/trove4j-1.0.20200330.jar, took 2 s 505 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (2.47 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/xerces/xercesImpl/2.12.0/xercesImpl-2.12.0.jar (6.31 kB / 1.39 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/xerces/xercesImpl/2.12.0/xercesImpl-2.12.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9823649442218369549bin
Download https://maven.aliyun.com/repository/gradle-plugin/xerces/xercesImpl/2.12.0/xercesImpl-2.12.0.jar, took 3 s 310 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-compress/1.20/commons-compress-1.20.jar (282.91 kB / 632.42 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-compress/1.20/commons-compress-1.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13379519328903847906bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/commons/commons-compress/1.20/commons-compress-1.20.jar, took 396 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar (629.89 kB / 780.32 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3505543848901355579bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar, took 587 ms
Download https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, took 345 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6938636162907660954bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/javax.activation/1.2.0/javax.activation-1.2.0.jar, took 323 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/activation/javax.activation/1.2.0/javax.activation-1.2.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download17377403787668305850bin
Download https://maven.aliyun.com/repository/google/com/android/signflinger/7.3.1/signflinger-7.3.1.jar, took 1 s 407 ms
Downloading https://maven.aliyun.com/repository/google/com/android/signflinger/7.3.1/signflinger-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download14899024509109296913bin
Downloading https://maven.aliyun.com/repository/google/com/android/zipflinger/7.3.1/zipflinger-7.3.1.jar (16.38 kB / 64.34 kB)Downloading https://maven.aliyun.com/repository/google/com/android/zipflinger/7.3.1/zipflinger-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download12584146683426722386bin
Download https://maven.aliyun.com/repository/google/com/android/zipflinger/7.3.1/zipflinger-7.3.1.jar, took 526 ms
Download https://maven.aliyun.com/repository/google/com/android/tools/annotations/30.3.1/annotations-30.3.1.jar, took 328 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/annotations/30.3.1/annotations-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5408109467495564783bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna-platform/5.6.0/jna-platform-5.6.0.jar (3.39 kB / 2.74 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna-platform/5.6.0/jna-platform-5.6.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10929467176089094800bin
Download https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna-platform/5.6.0/jna-platform-5.6.0.jar, took 2 s 404 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/juniversalchardet/juniversalchardet/1.0.3/juniversalchardet-1.0.3.jar (203.81 kB / 220.81 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/juniversalchardet/juniversalchardet/1.0.3/juniversalchardet-1.0.3.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10428069496947179855bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/juniversalchardet/juniversalchardet/1.0.3/juniversalchardet-1.0.3.jar, took 317 ms
Download https://maven.aliyun.com/repository/gradle-plugin/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, took 318 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7565974881054190811bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jvnet/staxex/stax-ex/1.8.1/stax-ex-1.8.1.jar, took 314 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jvnet/staxex/stax-ex/1.8.1/stax-ex-1.8.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3645081962881539668bin
Download https://maven.aliyun.com/repository/gradle-plugin/jakarta/xml/bind/jakarta.xml.bind-api/2.3.2/jakarta.xml.bind-api-2.3.2.jar, took 323 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (2.58 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/jakarta/xml/bind/jakarta.xml.bind-api/2.3.2/jakarta.xml.bind-api-2.3.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1751607184899794395bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/txw2/2.3.2/txw2-2.3.2.jar, took 376 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/glassfish/jaxb/txw2/2.3.2/txw2-2.3.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5210103096135923488bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/istack/istack-commons-runtime/3.0.8/istack-commons-runtime-3.0.8.jar, took 329 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/istack/istack-commons-runtime/3.0.8/istack-commons-runtime-3.0.8.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3215967432024422195bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/fastinfoset/FastInfoset/1.2.16/FastInfoset-1.2.16.jar, took 350 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/sun/xml/fastinfoset/FastInfoset/1.2.16/FastInfoset-1.2.16.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download18171873731314777008bin
Download https://maven.aliyun.com/repository/gradle-plugin/jakarta/activation/jakarta.activation-api/1.2.1/jakarta.activation-api-1.2.1.jar, took 365 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/jakarta/activation/jakarta.activation-api/1.2.1/jakarta.activation-api-1.2.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5086074015519552577bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/value/auto-value-annotations/1.6.2/auto-value-annotations-1.6.2.jar, took 303 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/auto/value/auto-value-annotations/1.6.2/auto-value-annotations-1.6.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10347342144414249821bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0.jar, took 327 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/errorprone/error_prone_annotations/2.4.0/error_prone_annotations-2.4.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5676979029640581893bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bitbucket/b_c/jose4j/0.7.0/jose4j-0.7.0.jar (181.50 kB / 265.69 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/bitbucket/b_c/jose4j/0.7.0/jose4j-0.7.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download17500643383337031557bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/bitbucket/b_c/jose4j/0.7.0/jose4j-0.7.0.jar, took 607 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar (16.38 kB / 41.47 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download18134053754179437837bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, took 399 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jdom/jdom2/2.0.6/jdom2-2.0.6.jar (100.23 kB / 304.92 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jdom/jdom2/2.0.6/jdom2-2.0.6.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15104098556809219900bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jdom/jdom2/2.0.6/jdom2-2.0.6.jar, took 541 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar, took 343 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15464499715829699850bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/animal-sniffer-annotations/1.19/animal-sniffer-annotations-1.19.jar, took 344 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/codehaus/mojo/animal-sniffer-annotations/1.19/animal-sniffer-annotations-1.19.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5703343105397404148bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/perfmark/perfmark-api/0.23.0/perfmark-api-0.23.0.jar, took 1 s 602 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (2.86 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/io/perfmark/perfmark-api/0.23.0/perfmark-api-0.23.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download18260041680854593141bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http2/4.1.52.Final/netty-codec-http2-4.1.52.Final.jar (43.45 kB / 456.93 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http2/4.1.52.Final/netty-codec-http2-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download11030460701116314130bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http2/4.1.52.Final/netty-codec-http2-4.1.52.Final.jar, took 1 s 89 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler-proxy/4.1.52.Final/netty-handler-proxy-4.1.52.Final.jar, took 572 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler-proxy/4.1.52.Final/netty-handler-proxy-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15302147472407859527bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, took 319 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15596226305602853078bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/json/json/20180813/json-20180813.jar, took 308 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/json/json/20180813/json-20180813.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download4348321706974450438bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar, took 307 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download322670089760949679bin
Download https://maven.aliyun.com/repository/gradle-plugin/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, took 308 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-codec/commons-codec/1.11/commons-codec-1.11.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15018492695979049977bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (3.14 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/it/unimi/dsi/fastutil/8.4.0/fastutil-8.4.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15048287975045132557bin
Download https://maven.aliyun.com/repository/gradle-plugin/it/unimi/dsi/fastutil/8.4.0/fastutil-8.4.0.jar, took 9 s 378 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/json-simple/json-simple/1.1/json-simple-1.1.jar, took 284 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/googlecode/json-simple/json-simple/1.1/json-simple-1.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download12416960107937429998bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (3.42 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/apksig/7.3.1/apksig-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download2132981724664943624bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/apksig/7.3.1/apksig-7.3.1.jar, took 1 s 528 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javawriter/2.5.0/javawriter-2.5.0.jar, took 294 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/squareup/javawriter/2.5.0/javawriter-2.5.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13749209574146115219bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-annotations/1.8.20/kotlin-gradle-plugin-annotations-1.8.20.jar, took 1 s 731 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-gradle-plugin-annotations/1.8.20/kotlin-gradle-plugin-annotations-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6772801643033698294bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (3.75 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-native-utils/1.8.20/kotlin-native-utils-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10457921117295008800bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-native-utils/1.8.20/kotlin-native-utils-1.8.20.jar, took 2 s 359 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-io/1.8.20/kotlin-util-io-1.8.20.jar (11.76 kB / 54.16 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-io/1.8.20/kotlin-util-io-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7953254991564917875bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-util-io/1.8.20/kotlin-util-io-1.8.20.jar, took 2 s 305 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-embeddable/1.8.20/kotlin-daemon-embeddable-1.8.20.jar (11.76 kB / 395.04 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-embeddable/1.8.20/kotlin-daemon-embeddable-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15225147659090863179bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-embeddable/1.8.20/kotlin-daemon-embeddable-1.8.20.jar, took 2 s 636 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna/5.6.0/jna-5.6.0.jar (398.05 kB / 1.51 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna/5.6.0/jna-5.6.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3416852387708954531bin
Download https://maven.aliyun.com/repository/gradle-plugin/net/java/dev/jna/jna/5.6.0/jna-5.6.0.jar, took 843 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-client/1.8.20/kotlin-daemon-client-1.8.20.jar (19.95 kB / 579.88 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-client/1.8.20/kotlin-daemon-client-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download688669788759332578bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-daemon-client/1.8.20/kotlin-daemon-client-1.8.20.jar, took 2 s 736 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlinx/kotlinx-coroutines-core-jvm/1.5.0/kotlinx-coroutines-core-jvm-1.5.0.jar (162.83 kB / 1.48 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlinx/kotlinx-coroutines-core-jvm/1.5.0/kotlinx-coroutines-core-jvm-1.5.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7719655904388471926bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlinx/kotlinx-coroutines-core-jvm/1.5.0/kotlinx-coroutines-core-jvm-1.5.0.jar, took 1 s 175 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-jvm/1.8.20/kotlin-scripting-jvm-1.8.20.jar (40.43 kB / 200.88 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-jvm/1.8.20/kotlin-scripting-jvm-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download5084802543597945583bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-jvm/1.8.20/kotlin-scripting-jvm-1.8.20.jar, took 2 s 47 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-common/1.8.20/kotlin-scripting-common-1.8.20.jar (23.15 kB / 226.89 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-common/1.8.20/kotlin-scripting-common-1.8.20.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13785764761499068872bin
Download https://maven.aliyun.com/repository/gradle-plugin/org/jetbrains/kotlin/kotlin-scripting-common/1.8.20/kotlin-scripting-common-1.8.20.jar, took 2 s 342 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar (1.91 kB / 4.62 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download12564750855915834515bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar, took 799 ms
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, took 286 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9665413990633583470bin
Download https://maven.aliyun.com/repository/gradle-plugin/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar, took 1 s 758 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download2283158729743205398bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.jar (22.68 kB / 220.54 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download8072411213974001447bin
Download https://maven.aliyun.com/repository/gradle-plugin/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.jar, took 994 ms
Download https://maven.aliyun.com/repository/gradle-plugin/commons-logging/commons-logging/1.2/commons-logging-1.2.jar, took 367 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/commons-logging/commons-logging/1.2/commons-logging-1.2.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13277869063808294460bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-context/1.39.0/grpc-context-1.39.0.jar, took 365 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/grpc/grpc-context/1.39.0/grpc-context-1.39.0.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download18401978798000592053bin
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http/4.1.52.Final/netty-codec-http-4.1.52.Final.jar (113.69 kB / 618.15 kB)Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http/4.1.52.Final/netty-codec-http-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download6847795025043355378bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-http/4.1.52.Final/netty-codec-http-4.1.52.Final.jar, took 819 ms
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler/4.1.52.Final/netty-handler-4.1.52.Final.jar, took 323 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-handler/4.1.52.Final/netty-handler-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9725781341270933463bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-socks/4.1.52.Final/netty-codec-socks-4.1.52.Final.jar, took 363 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec-socks/4.1.52.Final/netty-codec-socks-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1388330524399743470bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec/4.1.52.Final/netty-codec-4.1.52.Final.jar, took 325 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-codec/4.1.52.Final/netty-codec-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3046216827152905227bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-transport/4.1.52.Final/netty-transport-4.1.52.Final.jar, took 339 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (4.15 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-transport/4.1.52.Final/netty-transport-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15453003063808042942bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-buffer/4.1.52.Final/netty-buffer-4.1.52.Final.jar, took 337 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-buffer/4.1.52.Final/netty-buffer-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download3509531814498439385bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-resolver/4.1.52.Final/netty-resolver-4.1.52.Final.jar, took 391 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-resolver/4.1.52.Final/netty-resolver-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7805315540421040821bin
Download https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-common/4.1.52.Final/netty-common-4.1.52.Final.jar, took 392 ms
Downloading https://maven.aliyun.com/repository/gradle-plugin/io/netty/netty-common/4.1.52.Final/netty-common-4.1.52.Final.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1687401262311630393bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-processor/1.0.0-beta10/jetifier-processor-1.0.0-beta10.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10182589956715934012bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-processor/1.0.0-beta10/jetifier-processor-1.0.0-beta10.jar, took 1 s 12 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/common/30.3.1/common-30.3.1.jar (151.02 kB / 387.16 kB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/common/30.3.1/common-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download9773458878490124137bin
Download https://maven.aliyun.com/repository/google/com/android/tools/common/30.3.1/common-30.3.1.jar, took 578 ms
Download https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-core/1.0.0-beta10/jetifier-core-1.0.0-beta10.jar, took 289 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/jetifier/jetifier-core/1.0.0-beta10/jetifier-core-1.0.0-beta10.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10876399707443971677bin
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (4.21 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/dvlib/30.3.1/dvlib-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7414203838312914046bin
Download https://maven.aliyun.com/repository/google/com/android/tools/dvlib/30.3.1/dvlib-30.3.1.jar, took 517 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-model/30.3.1/lint-model-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10565823142877464628bin
Download https://maven.aliyun.com/repository/google/com/android/tools/lint/lint-model/30.3.1/lint-model-30.3.1.jar, took 1 s 121 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/layoutlib/layoutlib-api/30.3.1/layoutlib-api-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download13910931510854500060bin
Download https://maven.aliyun.com/repository/google/com/android/tools/layoutlib/layoutlib-api/30.3.1/layoutlib-api-30.3.1.jar, took 2 s 125 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (4.37 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder/7.3.1/builder-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1900404269431241026bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder/7.3.1/builder-7.3.1.jar, took 5 s 987 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (4.99 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder-model/7.3.1/builder-model-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15841974610056677018bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder-model/7.3.1/builder-model-7.3.1.jar, took 5 s 303 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (5.40 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/sdklib/30.3.1/sdklib-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download16374907566574771566bin
Download https://maven.aliyun.com/repository/google/com/android/tools/sdklib/30.3.1/sdklib-30.3.1.jar, took 5 s 838 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (5.63 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-retention-proto/30.3.1/android-test-plugin-host-retention-proto-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10521536573834784928bin
Download https://maven.aliyun.com/repository/google/com/android/tools/utp/android-test-plugin-host-retention-proto/30.3.1/android-test-plugin-host-retention-proto-30.3.1.jar, took 3 s 695 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (5.88 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/sdk-common/30.3.1/sdk-common-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download10480549574938562976bin
Download https://maven.aliyun.com/repository/google/com/android/tools/sdk-common/30.3.1/sdk-common-30.3.1.jar, took 3 s 608 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (6.78 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/shared/30.3.1/shared-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download500061077530318509bin
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/shared/30.3.1/shared-30.3.1.jar, took 1 s 781 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/repository/30.3.1/repository-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download15237099195330796781bin
Download https://maven.aliyun.com/repository/google/com/android/tools/repository/30.3.1/repository-30.3.1.jar, took 1 s 853 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/tracker/30.3.1/tracker-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download14367297140018379338bin
Download https://maven.aliyun.com/repository/google/com/android/tools/analytics-library/tracker/30.3.1/tracker-30.3.1.jar, took 1 s 390 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (6.88 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/manifest-merger/30.3.1/manifest-merger-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download4360095589897056797bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/manifest-merger/30.3.1/manifest-merger-30.3.1.jar, took 3 s 549 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.jar (98.14 kB / 492.72 kB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download1030825305019964946bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/aaptcompiler/7.3.1/aaptcompiler-7.3.1.jar, took 1 s 596 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (7.17 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle-api/7.3.1/gradle-api-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download7074020565622752017bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/gradle-api/7.3.1/gradle-api-7.3.1.jar, took 1 s 49 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/builder-test-api/7.3.1/builder-test-api-7.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download4070571681473589583bin
Download https://maven.aliyun.com/repository/google/com/android/tools/build/builder-test-api/7.3.1/builder-test-api-7.3.1.jar, took 2 s 852 ms
Downloading https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar (7.56 MB / 9.32 MB)Downloading https://maven.aliyun.com/repository/google/com/android/tools/ddms/ddmlib/30.3.1/ddmlib-30.3.1.jar to C:\Users\<USER>\.gradle\.tmp\gradle_download12935535522707146072bin
Download https://maven.aliyun.com/repository/google/com/android/tools/ddms/ddmlib/30.3.1/ddmlib-30.3.1.jar, took 1 s 517 ms
Download https://maven.aliyun.com/repository/google/com/android/tools/build/gradle/7.3.1/gradle-7.3.1.jar, took 6 m 26 s 529 ms
Caching disabled for Kotlin DSL accessors for root project 'M&A' because:
  Build cache is disabled
Kotlin DSL accessors for root project 'M&A' is not up-to-date because:
  No history is available.
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\0d9b49f02532aa8aae98036628632d41\gradle-7.3.1.jar: class com/android/build/gradle/internal/tasks/factory/TaskFactoryUtils: inline fun letIfPresent(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\86acb1205765deff603bffcdb99694d7\common-30.3.1.jar: class com/android/utils/NodeCollectionHelperKt: inline fun forEach(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\674bbccf88a6ad54476be288d47477fd\kotlin-stdlib-jdk8-1.7.10.jar: class kotlin/jvm/optionals/OptionalsKt: inline fun getOrElse(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\187243a08a56da5c85de218a011ee617\kotlin-reflect-1.7.10.jar: class kotlin/reflect/full/KAnnotatedElements: inline fun findAnnotation(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\f041a2017f2a76b415e56e90609ba18b\kotlin-stdlib-1.7.10.jar: class kotlin/ArrayIntrinsicsKt: inline fun emptyArray(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\a7d7ae63d6674ccacb2f750d537e81d4\kotlin-gradle-plugin-1.8.20-gradle76.jar: class org/jetbrains/kotlin/gradle/internal/KaptWithAndroid: inline fun androidVariantData(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\8f9100b6ffe268ff564c6361da407bfb\bundletool-1.9.0.jar: class shadow/bundletool/com/android/utils/NodeCollectionHelperKt: inline fun forEach(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\f0d7ef384abc3814d9a713800709abd5\kotlin-gradle-plugin-idea-proto-1.8.20.jar: class org/jetbrains/kotlin/gradle/idea/proto/generated/IdeaExtrasProtoKt$Dsl: inline fun setValues(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\3c089c93748420268854f9a8715bbed1\kotlin-gradle-plugin-idea-1.8.20.jar: class org/jetbrains/kotlin/gradle/idea/serialize/IdeaKotlinExtrasSerializer$Companion: inline fun javaIoSerializable(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\430f49862fa596a55c3a5f925653dc74\kotlin-tooling-core-1.8.20.jar: class org/jetbrains/kotlin/tooling/core/ClosureKt: inline fun closure(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\cc5e1dbff66f15fcc78264f2e87b5234\kotlin-annotation-processing-gradle-1.8.20.jar: class org/jetbrains/kotlin/kapt3/base/JavacListUtilsKt: inline fun mapJList(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\5873016c049ba095b90181fcb66e603f\kotlin-android-extensions-1.8.20.jar: class org/jetbrains/kotlin/android/parcel/ir/IrUtilsKt: inline fun forUntil(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\96bfe504dded7023c3e3c87626bf71e8\kotlin-compiler-embeddable-1.8.20.jar: class org/jetbrains/kotlin/KtSourceElementKt: inline fun toKtLightSourceElement(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\284d4320b6116fcac6027b797a1138a0\kotlin-scripting-compiler-embeddable-1.8.20.jar: class org/jetbrains/kotlin/scripting/compiler/plugin/impl/JvmCompilationUtilKt: inline fun withMessageCollector(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\ad9b20455ff8b8483d45b4113969d9da\kotlin-scripting-compiler-impl-embeddable-1.8.20.jar: class org/jetbrains/kotlin/scripting/definitions/DefinitionsKt: inline fun runReadAction(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\a72e00fa97f7b42719627dceae59fa7b\kotlin-util-io-1.8.20.jar: class org/jetbrains/kotlin/konan/file/FileKt: inline fun use(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\3a02103cb2d59f778fdaf76f7accfc96\kotlin-daemon-embeddable-1.8.20.jar: class org/jetbrains/kotlin/daemon/CompileServiceImplBase: inline fun checkedCompile(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\d2d364a43f73f83df7ca7c68c55fc82f\kotlin-daemon-client-1.8.20.jar: class org/jetbrains/kotlin/daemon/common/DaemonParamsKt: inline fun findWithTransform(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\11dcf3a52353ea12cff67905e5217774\kotlinx-coroutines-core-jvm-1.5.0.jar: class kotlinx/coroutines/CoroutineScopeKt: inline fun currentCoroutineContext(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\72b3ef1d7b877ae3639acef0ce06c232\kotlin-scripting-jvm-1.8.20.jar: class kotlin/script/experimental/jvm/util/JvmClasspathUtilKt: inline fun classpathFromClass(): compile avoidance is not supported with public inline functions
Cannot use Kotlin build script compile avoidance with C:\Users\<USER>\.gradle\caches\jars-9\c1328b0c3b706a8cbd8b5e28050b0569\kotlin-scripting-common-1.8.20.jar: class kotlin/script/experimental/api/ErrorHandlingKt: inline fun flatMapSuccess(): compile avoidance is not supported with public inline functions
Caching disabled for Kotlin DSL script compilation (Project/TopLevel/stage2) because:
  Build cache is disabled
Kotlin DSL script compilation (Project/TopLevel/stage2) is not up-to-date because:
  No history is available.

FAILURE: Build failed with an exception.

* Where:
Build file 'C:\Users\<USER>\AndroidStudioProjects\MA\build.gradle.kts' line: 10

* What went wrong:
Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by build file 'build.gradle.kts'

* Try:
> Run with --debug option to get more log output.
> Run with --scan to get full insights.

* Exception is:
org.gradle.api.InvalidUserCodeException: Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by build file 'build.gradle.kts'
	at org.gradle.internal.management.DefaultDependencyResolutionManagement.repoMutationDisallowedOnProject(DefaultDependencyResolutionManagement.java:199)
	at org.gradle.internal.ImmutableActionSet$SetWithFewActions.execute(ImmutableActionSet.java:285)
	at org.gradle.api.internal.DefaultDomainObjectCollection.doAdd(DefaultDomainObjectCollection.java:262)
	at org.gradle.api.internal.DefaultNamedDomainObjectCollection.doAdd(DefaultNamedDomainObjectCollection.java:113)
	at org.gradle.api.internal.DefaultDomainObjectCollection.add(DefaultDomainObjectCollection.java:251)
	at org.gradle.api.internal.artifacts.DefaultArtifactRepositoryContainer.access$101(DefaultArtifactRepositoryContainer.java:35)
	at org.gradle.api.internal.artifacts.DefaultArtifactRepositoryContainer.lambda$new$0(DefaultArtifactRepositoryContainer.java:38)
	at org.gradle.api.internal.artifacts.DefaultArtifactRepositoryContainer.addWithUniqueName(DefaultArtifactRepositoryContainer.java:101)
	at org.gradle.api.internal.artifacts.DefaultArtifactRepositoryContainer.addRepository(DefaultArtifactRepositoryContainer.java:89)
	at org.gradle.api.internal.artifacts.dsl.DefaultRepositoryHandler.maven(DefaultRepositoryHandler.java:161)
	at Build_gradle$1$1.invoke(build.gradle.kts:10)
	at Build_gradle$1$1.invoke(build.gradle.kts:1)
	at org.gradle.kotlin.dsl.ProjectExtensionsKt.repositories(ProjectExtensions.kt:159)
	at Build_gradle$1.execute(build.gradle.kts:8)
	at Build_gradle$1.execute(build.gradle.kts:1)
	at org.gradle.api.internal.DefaultMutationGuard$1.execute(DefaultMutationGuard.java:45)
	at org.gradle.internal.Actions.with(Actions.java:249)
	at org.gradle.api.internal.project.BuildOperationCrossProjectConfigurator$1.run(BuildOperationCrossProjectConfigurator.java:69)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:68)
	at org.gradle.api.internal.project.BuildOperationCrossProjectConfigurator.lambda$runProjectConfigureAction$0(BuildOperationCrossProjectConfigurator.java:66)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$applyToMutableState$0(DefaultProjectStateRegistry.java:360)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.fromMutableState(DefaultProjectStateRegistry.java:378)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.applyToMutableState(DefaultProjectStateRegistry.java:359)
	at org.gradle.api.internal.project.BuildOperationCrossProjectConfigurator.runProjectConfigureAction(BuildOperationCrossProjectConfigurator.java:66)
	at org.gradle.api.internal.project.BuildOperationCrossProjectConfigurator.access$100(BuildOperationCrossProjectConfigurator.java:32)
	at org.gradle.api.internal.project.BuildOperationCrossProjectConfigurator$BlockConfigureBuildOperation.run(BuildOperationCrossProjectConfigurator.java:111)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:68)
	at org.gradle.api.internal.project.BuildOperationCrossProjectConfigurator.runBlockConfigureAction(BuildOperationCrossProjectConfigurator.java:62)
	at org.gradle.api.internal.project.BuildOperationCrossProjectConfigurator.allprojects(BuildOperationCrossProjectConfigurator.java:53)
	at org.gradle.api.internal.project.DefaultProject.allprojects(DefaultProject.java:703)
	at org.gradle.api.internal.project.DefaultProject.allprojects(DefaultProject.java:698)
	at Build_gradle.<init>(build.gradle.kts:7)
	at Program.execute(Unknown Source)
	at org.gradle.kotlin.dsl.execution.Interpreter$ProgramHost.eval(Interpreter.kt:532)
	at org.gradle.kotlin.dsl.execution.Interpreter$ProgramHost.evaluateSecondStageOf(Interpreter.kt:456)
	at Program.execute(Unknown Source)
	at org.gradle.kotlin.dsl.execution.Interpreter$ProgramHost.eval(Interpreter.kt:532)
	at org.gradle.kotlin.dsl.execution.Interpreter.eval(Interpreter.kt:205)
	at org.gradle.kotlin.dsl.provider.StandardKotlinScriptEvaluator.evaluate(KotlinScriptEvaluator.kt:115)
	at org.gradle.kotlin.dsl.provider.KotlinScriptPluginFactory$create$1.invoke(KotlinScriptPluginFactory.kt:51)
	at org.gradle.kotlin.dsl.provider.KotlinScriptPluginFactory$create$1.invoke(KotlinScriptPluginFactory.kt:36)
	at org.gradle.kotlin.dsl.provider.KotlinScriptPlugin.apply(KotlinScriptPlugin.kt:34)
	at org.gradle.configuration.BuildOperationScriptPlugin$1.run(BuildOperationScriptPlugin.java:65)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:68)
	at org.gradle.configuration.BuildOperationScriptPlugin.lambda$apply$0(BuildOperationScriptPlugin.java:62)
	at org.gradle.configuration.internal.DefaultUserCodeApplicationContext.apply(DefaultUserCodeApplicationContext.java:44)
	at org.gradle.configuration.BuildOperationScriptPlugin.apply(BuildOperationScriptPlugin.java:62)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$applyToMutableState$0(DefaultProjectStateRegistry.java:360)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.fromMutableState(DefaultProjectStateRegistry.java:378)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.applyToMutableState(DefaultProjectStateRegistry.java:359)
	at org.gradle.configuration.project.BuildScriptProcessor.execute(BuildScriptProcessor.java:42)
	at org.gradle.configuration.project.BuildScriptProcessor.execute(BuildScriptProcessor.java:26)
	at org.gradle.configuration.project.ConfigureActionsProjectEvaluator.evaluate(ConfigureActionsProjectEvaluator.java:35)
	at org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.lambda$run$0(LifecycleProjectEvaluator.java:109)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$applyToMutableState$0(DefaultProjectStateRegistry.java:360)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$fromMutableState$1(DefaultProjectStateRegistry.java:383)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withReplacedLocks(DefaultWorkerLeaseService.java:345)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.fromMutableState(DefaultProjectStateRegistry.java:383)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.applyToMutableState(DefaultProjectStateRegistry.java:359)
	at org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.run(LifecycleProjectEvaluator.java:100)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:68)
	at org.gradle.configuration.project.LifecycleProjectEvaluator.evaluate(LifecycleProjectEvaluator.java:72)
	at org.gradle.api.internal.project.DefaultProject.evaluate(DefaultProject.java:762)
	at org.gradle.api.internal.project.DefaultProject.evaluate(DefaultProject.java:153)
	at org.gradle.api.internal.project.ProjectLifecycleController.lambda$ensureSelfConfigured$1(ProjectLifecycleController.java:63)
	at org.gradle.internal.model.StateTransitionController.lambda$doTransition$12(StateTransitionController.java:236)
	at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:247)
	at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:235)
	at org.gradle.internal.model.StateTransitionController.lambda$maybeTransitionIfNotCurrentlyTransitioning$9(StateTransitionController.java:196)
	at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
	at org.gradle.internal.model.StateTransitionController.maybeTransitionIfNotCurrentlyTransitioning(StateTransitionController.java:192)
	at org.gradle.api.internal.project.ProjectLifecycleController.ensureSelfConfigured(ProjectLifecycleController.java:63)
	at org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.ensureConfigured(DefaultProjectStateRegistry.java:334)
	at org.gradle.execution.TaskPathProjectEvaluator.configure(TaskPathProjectEvaluator.java:33)
	at org.gradle.execution.TaskPathProjectEvaluator.configureHierarchy(TaskPathProjectEvaluator.java:47)
	at org.gradle.configuration.DefaultProjectsPreparer.prepareProjects(DefaultProjectsPreparer.java:50)
	at org.gradle.configuration.BuildTreePreparingProjectsPreparer.prepareProjects(BuildTreePreparingProjectsPreparer.java:64)
	at org.gradle.configuration.BuildOperationFiringProjectsPreparer$ConfigureBuild.run(BuildOperationFiringProjectsPreparer.java:52)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:68)
	at org.gradle.configuration.BuildOperationFiringProjectsPreparer.prepareProjects(BuildOperationFiringProjectsPreparer.java:40)
	at org.gradle.initialization.VintageBuildModelController.lambda$prepareProjects$2(VintageBuildModelController.java:84)
	at org.gradle.internal.model.StateTransitionController.lambda$doTransition$12(StateTransitionController.java:236)
	at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:247)
	at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:235)
	at org.gradle.internal.model.StateTransitionController.lambda$transitionIfNotPreviously$10(StateTransitionController.java:210)
	at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
	at org.gradle.internal.model.StateTransitionController.transitionIfNotPreviously(StateTransitionController.java:206)
	at org.gradle.initialization.VintageBuildModelController.prepareProjects(VintageBuildModelController.java:84)
	at org.gradle.initialization.VintageBuildModelController.getConfiguredModel(VintageBuildModelController.java:64)
	at org.gradle.internal.build.DefaultBuildLifecycleController.lambda$withProjectsConfigured$1(DefaultBuildLifecycleController.java:116)
	at org.gradle.internal.model.StateTransitionController.lambda$notInState$3(StateTransitionController.java:143)
	at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:44)
	at org.gradle.internal.model.StateTransitionController.notInState(StateTransitionController.java:139)
	at org.gradle.internal.build.DefaultBuildLifecycleController.withProjectsConfigured(DefaultBuildLifecycleController.java:116)
	at org.gradle.internal.build.DefaultBuildToolingModelController.locateBuilderForTarget(DefaultBuildToolingModelController.java:57)
	at org.gradle.internal.buildtree.DefaultBuildTreeModelCreator$DefaultBuildTreeModelController.lambda$locateBuilderForTarget$0(DefaultBuildTreeModelCreator.java:73)
	at org.gradle.internal.build.DefaultBuildLifecycleController.withToolingModels(DefaultBuildLifecycleController.java:180)
	at org.gradle.internal.build.AbstractBuildState.withToolingModels(AbstractBuildState.java:123)
	at org.gradle.internal.buildtree.DefaultBuildTreeModelCreator$DefaultBuildTreeModelController.locateBuilderForTarget(DefaultBuildTreeModelCreator.java:73)
	at org.gradle.internal.buildtree.DefaultBuildTreeModelCreator$DefaultBuildTreeModelController.locateBuilderForDefaultTarget(DefaultBuildTreeModelCreator.java:68)
	at org.gradle.tooling.internal.provider.runner.DefaultBuildController.getTarget(DefaultBuildController.java:157)
	at org.gradle.tooling.internal.provider.runner.DefaultBuildController.getModel(DefaultBuildController.java:101)
	at org.gradle.tooling.internal.consumer.connection.ParameterAwareBuildControllerAdapter.getModel(ParameterAwareBuildControllerAdapter.java:40)
	at org.gradle.tooling.internal.consumer.connection.UnparameterizedBuildController.getModel(UnparameterizedBuildController.java:116)
	at org.gradle.tooling.internal.consumer.connection.NestedActionAwareBuildControllerAdapter.getModel(NestedActionAwareBuildControllerAdapter.java:32)
	at org.gradle.tooling.internal.consumer.connection.UnparameterizedBuildController.getModel(UnparameterizedBuildController.java:79)
	at org.gradle.tooling.internal.consumer.connection.NestedActionAwareBuildControllerAdapter.getModel(NestedActionAwareBuildControllerAdapter.java:32)
	at org.gradle.tooling.internal.consumer.connection.UnparameterizedBuildController.getModel(UnparameterizedBuildController.java:64)
	at org.gradle.tooling.internal.consumer.connection.NestedActionAwareBuildControllerAdapter.getModel(NestedActionAwareBuildControllerAdapter.java:32)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.lambda$initAction$6(GradleModelFetchAction.java:185)
	at com.intellij.gradle.toolingExtension.impl.telemetry.GradleOpenTelemetry.callWithSpan(GradleOpenTelemetry.java:74)
	at com.intellij.gradle.toolingExtension.impl.telemetry.GradleOpenTelemetry.callWithSpan(GradleOpenTelemetry.java:62)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.initAction(GradleModelFetchAction.java:184)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.doExecute(GradleModelFetchAction.java:139)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.lambda$execute$1(GradleModelFetchAction.java:104)
	at com.intellij.gradle.toolingExtension.impl.telemetry.GradleOpenTelemetry.callWithSpan(GradleOpenTelemetry.java:74)
	at com.intellij.gradle.toolingExtension.impl.telemetry.GradleOpenTelemetry.callWithSpan(GradleOpenTelemetry.java:62)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.lambda$execute$2(GradleModelFetchAction.java:103)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.withOpenTelemetry(GradleModelFetchAction.java:114)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.lambda$execute$3(GradleModelFetchAction.java:102)
	at com.intellij.gradle.toolingExtension.impl.util.GradleExecutorServiceUtil.withSingleThreadExecutor(GradleExecutorServiceUtil.java:18)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.execute(GradleModelFetchAction.java:101)
	at com.intellij.gradle.toolingExtension.impl.modelAction.GradleModelFetchAction.execute(GradleModelFetchAction.java:37)
	at org.gradle.tooling.internal.consumer.connection.InternalBuildActionAdapter.execute(InternalBuildActionAdapter.java:65)
	at org.gradle.tooling.internal.provider.runner.AbstractClientProvidedBuildActionRunner$ActionAdapter.runAction(AbstractClientProvidedBuildActionRunner.java:131)
	at org.gradle.tooling.internal.provider.runner.AbstractClientProvidedBuildActionRunner$ActionAdapter.beforeTasks(AbstractClientProvidedBuildActionRunner.java:99)
	at org.gradle.internal.buildtree.DefaultBuildTreeModelCreator.beforeTasks(DefaultBuildTreeModelCreator.java:52)
	at org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$fromBuildModel$2(DefaultBuildTreeLifecycleController.java:82)
	at org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$runBuild$5(DefaultBuildTreeLifecycleController.java:113)
	at org.gradle.internal.model.StateTransitionController.lambda$transition$5(StateTransitionController.java:166)
	at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:247)
	at org.gradle.internal.model.StateTransitionController.lambda$transition$6(StateTransitionController.java:166)
	at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:44)
	at org.gradle.internal.model.StateTransitionController.transition(StateTransitionController.java:166)
	at org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.runBuild(DefaultBuildTreeLifecycleController.java:110)
	at org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.fromBuildModel(DefaultBuildTreeLifecycleController.java:81)
	at org.gradle.tooling.internal.provider.runner.AbstractClientProvidedBuildActionRunner.runClientAction(AbstractClientProvidedBuildActionRunner.java:43)
	at org.gradle.tooling.internal.provider.runner.ClientProvidedPhasedActionRunner.run(ClientProvidedPhasedActionRunner.java:53)
	at org.gradle.launcher.exec.ChainingBuildActionRunner.run(ChainingBuildActionRunner.java:35)
	at org.gradle.internal.buildtree.ProblemReportingBuildActionRunner.run(ProblemReportingBuildActionRunner.java:49)
	at org.gradle.launcher.exec.BuildOutcomeReportingBuildActionRunner.run(BuildOutcomeReportingBuildActionRunner.java:65)
	at org.gradle.tooling.internal.provider.FileSystemWatchingBuildActionRunner.run(FileSystemWatchingBuildActionRunner.java:136)
	at org.gradle.launcher.exec.BuildCompletionNotifyingBuildActionRunner.run(BuildCompletionNotifyingBuildActionRunner.java:41)
	at org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.lambda$execute$0(RootBuildLifecycleBuildActionExecutor.java:40)
	at org.gradle.composite.internal.DefaultRootBuildState.run(DefaultRootBuildState.java:122)
	at org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.execute(RootBuildLifecycleBuildActionExecutor.java:40)
	at org.gradle.internal.buildtree.DefaultBuildTreeContext.execute(DefaultBuildTreeContext.java:40)
	at org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.lambda$execute$0(BuildTreeLifecycleBuildActionExecutor.java:65)
	at org.gradle.internal.buildtree.BuildTreeState.run(BuildTreeState.java:53)
	at org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.execute(BuildTreeLifecycleBuildActionExecutor.java:65)
	at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(RunAsBuildOperationBuildActionExecutor.java:61)
	at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(RunAsBuildOperationBuildActionExecutor.java:57)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:199)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.internal.operations.DefaultBuildOperationExecutor.call(DefaultBuildOperationExecutor.java:73)
	at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor.execute(RunAsBuildOperationBuildActionExecutor.java:57)
	at org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.lambda$execute$0(RunAsWorkerThreadBuildActionExecutor.java:36)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:249)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:109)
	at org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.execute(RunAsWorkerThreadBuildActionExecutor.java:36)
	at org.gradle.tooling.internal.provider.continuous.ContinuousBuildActionExecutor.execute(ContinuousBuildActionExecutor.java:110)
	at org.gradle.tooling.internal.provider.SubscribableBuildActionExecutor.execute(SubscribableBuildActionExecutor.java:64)
	at org.gradle.internal.session.DefaultBuildSessionContext.execute(DefaultBuildSessionContext.java:46)
	at org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(BuildSessionLifecycleBuildActionExecuter.java:100)
	at org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(BuildSessionLifecycleBuildActionExecuter.java:88)
	at org.gradle.internal.session.BuildSessionState.run(BuildSessionState.java:69)
	at org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(BuildSessionLifecycleBuildActionExecuter.java:62)
	at org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(BuildSessionLifecycleBuildActionExecuter.java:41)
	at org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(StartParamsValidatingActionExecuter.java:63)
	at org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(StartParamsValidatingActionExecuter.java:31)
	at org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(SessionFailureReportingActionExecuter.java:52)
	at org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(SessionFailureReportingActionExecuter.java:40)
	at org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(SetupLoggingActionExecuter.java:47)
	at org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(SetupLoggingActionExecuter.java:31)
	at org.gradle.launcher.daemon.server.exec.ExecuteBuild.doBuild(ExecuteBuild.java:65)
	at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.WatchForDisconnection.execute(WatchForDisconnection.java:39)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.ResetDeprecationLogger.execute(ResetDeprecationLogger.java:29)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.RequestStopIfSingleUsedDaemon.execute(RequestStopIfSingleUsedDaemon.java:35)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create(ForwardClientInput.java:78)
	at org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create(ForwardClientInput.java:75)
	at org.gradle.util.internal.Swapper.swap(Swapper.java:38)
	at org.gradle.launcher.daemon.server.exec.ForwardClientInput.execute(ForwardClientInput.java:75)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.LogAndCheckHealth.execute(LogAndCheckHealth.java:55)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.LogToClient.doBuild(LogToClient.java:63)
	at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment.doBuild(EstablishBuildEnvironment.java:84)
	at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
	at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
	at org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy$1.run(StartBuildOrRespondWithBusy.java:52)
	at org.gradle.launcher.daemon.server.DaemonStateCoordinator$1.run(DaemonStateCoordinator.java:297)
	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
	at org.gradle.internal.concurrent.ManagedExecutorImpl$1.run(ManagedExecutorImpl.java:49)


* Get more help at https://help.gradle.org

CONFIGURE FAILED in 12m 53s
Watched directory hierarchies: [C:\Users\<USER>\AndroidStudioProjects\MA]
   